<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Find Contractors - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="construction, contractors, Sri Lanka, verified contractors, CIDA" name="keywords">
    <meta content="Find Verified Contractors - Brick & Click" name="description">

    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border position-relative text-primary" style="width: 6rem; height: 6rem;" role="status"></div>
        <img class="position-absolute top-50 start-50 translate-middle" src="img/icons/icon-1.png" alt="Icon">
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5 wow fadeIn" data-wow-delay="0.1s">
        <a href="index.html" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="customer-dashboard.html" class="nav-item nav-link">Dashboard</a>
                <a href="search-contractors.html" class="nav-item nav-link active">Find Contractors</a>
                <a href="my-quotes.html" class="nav-item nav-link">My Quotes</a>
                <a href="my-favorites.html" class="nav-item nav-link">Favorites</a>
                <a href="cost-estimator.html" class="nav-item nav-link">Cost Estimator</a>
            </div>
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                    <i class="fa fa-user me-2"></i>John Doe
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="customer-profile.html"><i class="fa fa-user me-2"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="notifications.html"><i class="fa fa-bell me-2"></i>Notifications <span class="badge bg-danger">3</span></a></li>
                    <li><a class="dropdown-item" href="settings.html"><i class="fa fa-cog me-2"></i>Settings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="index.html"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Page Header Start -->
    <div class="container-fluid page-header py-5 mb-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <h1 class="display-1 text-white animated slideInDown">Find Contractors</h1>
            <nav aria-label="breadcrumb animated slideInDown">
                <ol class="breadcrumb text-uppercase mb-0">
                    <li class="breadcrumb-item"><a class="text-white" href="customer-dashboard.html">Dashboard</a></li>
                    <li class="breadcrumb-item text-primary active" aria-current="page">Find Contractors</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Search and Filter Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="row">
                <!-- Filters Sidebar -->
                <div class="col-lg-3 mb-5">
                    <div class="bg-light rounded p-4 sticky-top" style="top: 100px;">
                        <h4 class="text-primary mb-4">
                            <i class="fa fa-filter me-2"></i>Filter Contractors
                        </h4>

                        <!-- Search by Name -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Search by Name</label>
                            <input type="text" class="form-control" id="searchName" placeholder="Enter contractor name">
                        </div>

                        <!-- Location Filter -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Location</label>
                            <select class="form-select" id="locationFilter">
                                <option value="">All Districts</option>
                                <option value="colombo">Colombo</option>
                                <option value="gampaha">Gampaha</option>
                                <option value="kalutara">Kalutara</option>
                                <option value="kandy">Kandy</option>
                                <option value="matale">Matale</option>
                                <option value="nuwara-eliya">Nuwara Eliya</option>
                                <option value="galle">Galle</option>
                                <option value="matara">Matara</option>
                                <option value="hambantota">Hambantota</option>
                                <option value="jaffna">Jaffna</option>
                                <option value="kurunegala">Kurunegala</option>
                                <option value="anuradhapura">Anuradhapura</option>
                                <option value="ratnapura">Ratnapura</option>
                                <option value="kegalle">Kegalle</option>
                            </select>
                        </div>

                        <!-- Service Category Filter -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Service Category</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="residential" value="residential">
                                <label class="form-check-label" for="residential">Residential Construction</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="commercial" value="commercial">
                                <label class="form-check-label" for="commercial">Commercial Construction</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="renovation" value="renovation">
                                <label class="form-check-label" for="renovation">Renovation & Remodeling</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="electrical" value="electrical">
                                <label class="form-check-label" for="electrical">Electrical Work</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="plumbing" value="plumbing">
                                <label class="form-check-label" for="plumbing">Plumbing</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="interior" value="interior">
                                <label class="form-check-label" for="interior">Interior Design</label>
                            </div>
                        </div>

                        <!-- CIDA Grade Filter -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">CIDA Grade</label>
                            <select class="form-select" id="cidaGradeFilter">
                                <option value="">All Grades</option>
                                <option value="C1">C1</option>
                                <option value="C2">C2</option>
                                <option value="C3">C3</option>
                                <option value="C4">C4</option>
                                <option value="C5">C5</option>
                                <option value="C6">C6</option>
                                <option value="C7">C7</option>
                                <option value="C8">C8</option>
                                <option value="C9">C9</option>
                                <option value="C10">C10</option>
                            </select>
                        </div>

                        <!-- Rating Filter -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Minimum Rating</label>
                            <select class="form-select" id="ratingFilter">
                                <option value="">Any Rating</option>
                                <option value="4.5">4.5+ Stars</option>
                                <option value="4.0">4.0+ Stars</option>
                                <option value="3.5">3.5+ Stars</option>
                                <option value="3.0">3.0+ Stars</option>
                            </select>
                        </div>

                        <!-- Experience Filter -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Years of Experience</label>
                            <select class="form-select" id="experienceFilter">
                                <option value="">Any Experience</option>
                                <option value="1">1+ Years</option>
                                <option value="5">5+ Years</option>
                                <option value="10">10+ Years</option>
                                <option value="15">15+ Years</option>
                                <option value="20">20+ Years</option>
                            </select>
                        </div>

                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="applyFilters()">
                                <i class="fa fa-search me-2"></i>Apply Filters
                            </button>
                            <button class="btn btn-outline-secondary" onclick="clearFilters()">
                                <i class="fa fa-times me-2"></i>Clear All
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Results Area -->
                <div class="col-lg-9">
                    <!-- Results Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h3 class="mb-1">Search Results</h3>
                            <p class="text-muted mb-0" id="resultsCount">Showing 24 verified contractors</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <label class="me-2">Sort by:</label>
                            <select class="form-select w-auto" id="sortBy">
                                <option value="rating">Highest Rated</option>
                                <option value="reviews">Most Reviews</option>
                                <option value="experience">Most Experienced</option>
                                <option value="name">Name A-Z</option>
                                <option value="location">Location</option>
                            </select>
                        </div>
                    </div>

                    <!-- Contractors Grid -->
                    <div class="row g-4" id="contractorsGrid">
                        <!-- Contractor Card 1 -->
                        <div class="col-lg-6 contractor-card" data-location="colombo" data-services="residential,commercial" data-cida="C5" data-rating="4.8" data-experience="15">
                            <div class="bg-white rounded shadow-sm p-4 h-100">
                                <div class="d-flex align-items-start">
                                    <img src="img/team-1.jpg" alt="BuildPro Construction" class="rounded-circle me-3" style="width: 80px; height: 80px; object-fit: cover;">
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h5 class="mb-1">BuildPro Construction</h5>
                                            <button class="btn btn-outline-danger btn-sm favorite-btn" data-contractor="buildpro">
                                                <i class="fa fa-heart"></i>
                                            </button>
                                        </div>
                                        <p class="text-primary mb-2">Residential & Commercial Construction</p>
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="text-warning me-2">
                                                <i class="fa fa-star"></i>
                                                <i class="fa fa-star"></i>
                                                <i class="fa fa-star"></i>
                                                <i class="fa fa-star"></i>
                                                <i class="fa fa-star"></i>
                                            </div>
                                            <span class="fw-bold me-2">4.8</span>
                                            <small class="text-muted">(124 reviews)</small>
                                        </div>
                                        <div class="row text-center mb-3">
                                            <div class="col-4">
                                                <small class="text-muted d-block">CIDA Grade</small>
                                                <strong class="text-primary">C5</strong>
                                            </div>
                                            <div class="col-4">
                                                <small class="text-muted d-block">Experience</small>
                                                <strong class="text-primary">15 Years</strong>
                                            </div>
                                            <div class="col-4">
                                                <small class="text-muted d-block">Location</small>
                                                <strong class="text-primary">Colombo</strong>
                                            </div>
                                        </div>
                                        <div class="d-flex gap-2">
                                            <a href="contractor-profile.html?id=buildpro" class="btn btn-primary btn-sm flex-fill">View Profile</a>
                                            <a href="request-quote.html?contractor=buildpro" class="btn btn-outline-primary btn-sm flex-fill">Request Quote</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Contractor Card 2 -->
                        <div class="col-lg-6 contractor-card" data-location="kandy" data-services="renovation,interior" data-cida="C4" data-rating="4.9" data-experience="12">
                            <div class="bg-white rounded shadow-sm p-4 h-100">
                                <div class="d-flex align-items-start">
                                    <img src="img/team-2.jpg" alt="Lanka Builders" class="rounded-circle me-3" style="width: 80px; height: 80px; object-fit: cover;">
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h5 class="mb-1">Lanka Builders</h5>
                                            <button class="btn btn-outline-danger btn-sm favorite-btn" data-contractor="lanka">
                                                <i class="fa fa-heart"></i>
                                            </button>
                                        </div>
                                        <p class="text-primary mb-2">Renovation & Interior Design Specialists</p>
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="text-warning me-2">
                                                <i class="fa fa-star"></i>
                                                <i class="fa fa-star"></i>
                                                <i class="fa fa-star"></i>
                                                <i class="fa fa-star"></i>
                                                <i class="fa fa-star"></i>
                                            </div>
                                            <span class="fw-bold me-2">4.9</span>
                                            <small class="text-muted">(89 reviews)</small>
                                        </div>
                                        <div class="row text-center mb-3">
                                            <div class="col-4">
                                                <small class="text-muted d-block">CIDA Grade</small>
                                                <strong class="text-primary">C4</strong>
                                            </div>
                                            <div class="col-4">
                                                <small class="text-muted d-block">Experience</small>
                                                <strong class="text-primary">12 Years</strong>
                                            </div>
                                            <div class="col-4">
                                                <small class="text-muted d-block">Location</small>
                                                <strong class="text-primary">Kandy</strong>
                                            </div>
                                        </div>
                                        <div class="d-flex gap-2">
                                            <a href="contractor-profile.html?id=lanka" class="btn btn-primary btn-sm flex-fill">View Profile</a>
                                            <a href="request-quote.html?contractor=lanka" class="btn btn-outline-primary btn-sm flex-fill">Request Quote</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Contractor Card 3 -->
                        <div class="col-lg-6 contractor-card" data-location="colombo" data-services="commercial,electrical" data-cida="C6" data-rating="4.7" data-experience="20">
                            <div class="bg-white rounded shadow-sm p-4 h-100">
                                <div class="d-flex align-items-start">
                                    <img src="img/team-3.jpg" alt="Colombo Construction" class="rounded-circle me-3" style="width: 80px; height: 80px; object-fit: cover;">
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h5 class="mb-1">Colombo Construction</h5>
                                            <button class="btn btn-danger btn-sm favorite-btn favorited" data-contractor="colombo">
                                                <i class="fa fa-heart"></i>
                                            </button>
                                        </div>
                                        <p class="text-primary mb-2">Commercial Projects & Electrical Work</p>
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="text-warning me-2">
                                                <i class="fa fa-star"></i>
                                                <i class="fa fa-star"></i>
                                                <i class="fa fa-star"></i>
                                                <i class="fa fa-star"></i>
                                                <i class="fa fa-star-half-alt"></i>
                                            </div>
                                            <span class="fw-bold me-2">4.7</span>
                                            <small class="text-muted">(156 reviews)</small>
                                        </div>
                                        <div class="row text-center mb-3">
                                            <div class="col-4">
                                                <small class="text-muted d-block">CIDA Grade</small>
                                                <strong class="text-primary">C6</strong>
                                            </div>
                                            <div class="col-4">
                                                <small class="text-muted d-block">Experience</small>
                                                <strong class="text-primary">20 Years</strong>
                                            </div>
                                            <div class="col-4">
                                                <small class="text-muted d-block">Location</small>
                                                <strong class="text-primary">Colombo</strong>
                                            </div>
                                        </div>
                                        <div class="d-flex gap-2">
                                            <a href="contractor-profile.html?id=colombo" class="btn btn-primary btn-sm flex-fill">View Profile</a>
                                            <a href="request-quote.html?contractor=colombo" class="btn btn-outline-primary btn-sm flex-fill">Request Quote</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Contractor Card 4 -->
                        <div class="col-lg-6 contractor-card" data-location="gampaha" data-services="residential,plumbing" data-cida="C3" data-rating="4.6" data-experience="8">
                            <div class="bg-white rounded shadow-sm p-4 h-100">
                                <div class="d-flex align-items-start">
                                    <img src="img/team-4.jpg" alt="Gampaha Builders" class="rounded-circle me-3" style="width: 80px; height: 80px; object-fit: cover;">
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h5 class="mb-1">Gampaha Builders</h5>
                                            <button class="btn btn-outline-danger btn-sm favorite-btn" data-contractor="gampaha">
                                                <i class="fa fa-heart"></i>
                                            </button>
                                        </div>
                                        <p class="text-primary mb-2">Residential Construction & Plumbing</p>
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="text-warning me-2">
                                                <i class="fa fa-star"></i>
                                                <i class="fa fa-star"></i>
                                                <i class="fa fa-star"></i>
                                                <i class="fa fa-star"></i>
                                                <i class="fa fa-star-half-alt"></i>
                                            </div>
                                            <span class="fw-bold me-2">4.6</span>
                                            <small class="text-muted">(73 reviews)</small>
                                        </div>
                                        <div class="row text-center mb-3">
                                            <div class="col-4">
                                                <small class="text-muted d-block">CIDA Grade</small>
                                                <strong class="text-primary">C3</strong>
                                            </div>
                                            <div class="col-4">
                                                <small class="text-muted d-block">Experience</small>
                                                <strong class="text-primary">8 Years</strong>
                                            </div>
                                            <div class="col-4">
                                                <small class="text-muted d-block">Location</small>
                                                <strong class="text-primary">Gampaha</strong>
                                            </div>
                                        </div>
                                        <div class="d-flex gap-2">
                                            <a href="contractor-profile.html?id=gampaha" class="btn btn-primary btn-sm flex-fill">View Profile</a>
                                            <a href="request-quote.html?contractor=gampaha" class="btn btn-outline-primary btn-sm flex-fill">Request Quote</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="Contractors pagination" class="mt-5">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">Previous</a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#">Next</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    <!-- Search and Filter End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-body footer mt-5 pt-5 px-0 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Contact Us</h3>
                    <p class="mb-2"><i class="fa fa-map-marker-alt text-primary me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt text-primary me-3"></i>+94 77 123 4567</p>
                    <p class="mb-2"><i class="fa fa-envelope text-primary me-3"></i><EMAIL></p>
                    <div class="d-flex pt-2">
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-twitter"></i></a>
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-facebook-f"></i></a>
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-youtube"></i></a>
                        <a class="btn btn-square btn-outline-body me-0" href=""><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Quick Links</h3>
                    <a class="btn btn-link" href="about.html">About Us</a>
                    <a class="btn btn-link" href="contact.html">Contact Us</a>
                    <a class="btn btn-link" href="service.html">Our Services</a>
                    <a class="btn btn-link" href="">Terms & Condition</a>
                    <a class="btn btn-link" href="">Support</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">For Contractors</h3>
                    <a class="btn btn-link" href="register.html">Join as Contractor</a>
                    <a class="btn btn-link" href="">Contractor Dashboard</a>
                    <a class="btn btn-link" href="">Success Stories</a>
                    <a class="btn btn-link" href="">Contractor Resources</a>
                    <a class="btn btn-link" href="">FAQ</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Newsletter</h3>
                    <p>Stay updated with the latest construction news and opportunities.</p>
                    <div class="position-relative mx-auto" style="max-width: 400px;">
                        <input class="form-control bg-transparent w-100 py-3 ps-4 pe-5" type="text" placeholder="Your email">
                        <button type="button" class="btn btn-primary py-2 position-absolute top-0 end-0 mt-2 me-2">SignUp</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid copyright">
            <div class="container">
                <div class="row">
                    <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                        &copy; <a href="#">Brick & Click</a>, All Right Reserved.
                    </div>
                    <div class="col-md-6 text-center text-md-end">
                        Designed By <a href="https://htmlcodex.com">HTML Codex</a>
                        <br> Distributed By: <a class="border-bottom" href="https://themewagon.com" target="_blank">ThemeWagon</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>

    <!-- Template Javascript -->
    <script src="js/main.js"></script>

    <!-- Search Contractors Javascript -->
    <script>
        let allContractors = [];
        let filteredContractors = [];
        let favorites = JSON.parse(localStorage.getItem('favorites') || '[]');

        document.addEventListener('DOMContentLoaded', function() {
            loadContractors();
            initializeFilters();
            updateFavoriteButtons();
        });

        function loadContractors() {
            // In a real application, this would fetch from your backend API
            allContractors = [
                {
                    id: 'buildpro',
                    name: 'BuildPro Construction',
                    services: ['residential', 'commercial'],
                    location: 'colombo',
                    cidaGrade: 'C5',
                    rating: 4.8,
                    reviews: 124,
                    experience: 15,
                    image: 'img/team-1.jpg',
                    description: 'Residential & Commercial Construction'
                },
                {
                    id: 'lanka',
                    name: 'Lanka Builders',
                    services: ['renovation', 'interior'],
                    location: 'kandy',
                    cidaGrade: 'C4',
                    rating: 4.9,
                    reviews: 89,
                    experience: 12,
                    image: 'img/team-2.jpg',
                    description: 'Renovation & Interior Design Specialists'
                },
                {
                    id: 'colombo',
                    name: 'Colombo Construction',
                    services: ['commercial', 'electrical'],
                    location: 'colombo',
                    cidaGrade: 'C6',
                    rating: 4.7,
                    reviews: 156,
                    experience: 20,
                    image: 'img/team-3.jpg',
                    description: 'Commercial Projects & Electrical Work'
                },
                {
                    id: 'gampaha',
                    name: 'Gampaha Builders',
                    services: ['residential', 'plumbing'],
                    location: 'gampaha',
                    cidaGrade: 'C3',
                    rating: 4.6,
                    reviews: 73,
                    experience: 8,
                    image: 'img/team-4.jpg',
                    description: 'Residential Construction & Plumbing'
                }
            ];

            filteredContractors = [...allContractors];
            updateResultsCount();
        }

        function initializeFilters() {
            // Add event listeners for real-time filtering
            document.getElementById('searchName').addEventListener('input', debounce(applyFilters, 300));
            document.getElementById('locationFilter').addEventListener('change', applyFilters);
            document.getElementById('cidaGradeFilter').addEventListener('change', applyFilters);
            document.getElementById('ratingFilter').addEventListener('change', applyFilters);
            document.getElementById('experienceFilter').addEventListener('change', applyFilters);
            document.getElementById('sortBy').addEventListener('change', sortResults);

            // Service category checkboxes
            document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', applyFilters);
            });
        }

        function applyFilters() {
            const searchName = document.getElementById('searchName').value.toLowerCase();
            const location = document.getElementById('locationFilter').value;
            const cidaGrade = document.getElementById('cidaGradeFilter').value;
            const minRating = parseFloat(document.getElementById('ratingFilter').value) || 0;
            const minExperience = parseInt(document.getElementById('experienceFilter').value) || 0;

            // Get selected service categories
            const selectedServices = [];
            document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                selectedServices.push(checkbox.value);
            });

            filteredContractors = allContractors.filter(contractor => {
                // Name filter
                if (searchName && !contractor.name.toLowerCase().includes(searchName)) {
                    return false;
                }

                // Location filter
                if (location && contractor.location !== location) {
                    return false;
                }

                // CIDA grade filter
                if (cidaGrade && contractor.cidaGrade !== cidaGrade) {
                    return false;
                }

                // Rating filter
                if (contractor.rating < minRating) {
                    return false;
                }

                // Experience filter
                if (contractor.experience < minExperience) {
                    return false;
                }

                // Service category filter
                if (selectedServices.length > 0) {
                    const hasMatchingService = selectedServices.some(service =>
                        contractor.services.includes(service)
                    );
                    if (!hasMatchingService) {
                        return false;
                    }
                }

                return true;
            });

            sortResults();
            updateResultsCount();
            renderContractors();
        }

        function sortResults() {
            const sortBy = document.getElementById('sortBy').value;

            filteredContractors.sort((a, b) => {
                switch (sortBy) {
                    case 'rating':
                        return b.rating - a.rating;
                    case 'reviews':
                        return b.reviews - a.reviews;
                    case 'experience':
                        return b.experience - a.experience;
                    case 'name':
                        return a.name.localeCompare(b.name);
                    case 'location':
                        return a.location.localeCompare(b.location);
                    default:
                        return b.rating - a.rating;
                }
            });

            renderContractors();
        }

        function clearFilters() {
            document.getElementById('searchName').value = '';
            document.getElementById('locationFilter').value = '';
            document.getElementById('cidaGradeFilter').value = '';
            document.getElementById('ratingFilter').value = '';
            document.getElementById('experienceFilter').value = '';

            document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = false;
            });

            applyFilters();
        }

        function updateResultsCount() {
            const count = filteredContractors.length;
            document.getElementById('resultsCount').textContent =
                `Showing ${count} verified contractor${count !== 1 ? 's' : ''}`;
        }

        function renderContractors() {
            const grid = document.getElementById('contractorsGrid');

            if (filteredContractors.length === 0) {
                grid.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <i class="fa fa-search fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No contractors found</h4>
                        <p class="text-muted">Try adjusting your filters to see more results.</p>
                        <button class="btn btn-primary" onclick="clearFilters()">Clear Filters</button>
                    </div>
                `;
                return;
            }

            grid.innerHTML = filteredContractors.map(contractor => `
                <div class="col-lg-6 contractor-card" data-location="${contractor.location}"
                     data-services="${contractor.services.join(',')}" data-cida="${contractor.cidaGrade}"
                     data-rating="${contractor.rating}" data-experience="${contractor.experience}">
                    <div class="bg-white rounded shadow-sm p-4 h-100">
                        <div class="d-flex align-items-start">
                            <img src="${contractor.image}" alt="${contractor.name}"
                                 class="rounded-circle me-3" style="width: 80px; height: 80px; object-fit: cover;">
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h5 class="mb-1">${contractor.name}</h5>
                                    <button class="btn btn-outline-danger btn-sm favorite-btn ${favorites.includes(contractor.id) ? 'favorited btn-danger' : ''}"
                                            data-contractor="${contractor.id}">
                                        <i class="fa fa-heart"></i>
                                    </button>
                                </div>
                                <p class="text-primary mb-2">${contractor.description}</p>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="text-warning me-2">
                                        ${generateStars(contractor.rating)}
                                    </div>
                                    <span class="fw-bold me-2">${contractor.rating}</span>
                                    <small class="text-muted">(${contractor.reviews} reviews)</small>
                                </div>
                                <div class="row text-center mb-3">
                                    <div class="col-4">
                                        <small class="text-muted d-block">CIDA Grade</small>
                                        <strong class="text-primary">${contractor.cidaGrade}</strong>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted d-block">Experience</small>
                                        <strong class="text-primary">${contractor.experience} Years</strong>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted d-block">Location</small>
                                        <strong class="text-primary">${contractor.location.charAt(0).toUpperCase() + contractor.location.slice(1)}</strong>
                                    </div>
                                </div>
                                <div class="d-flex gap-2">
                                    <a href="contractor-profile.html?id=${contractor.id}" class="btn btn-primary btn-sm flex-fill">View Profile</a>
                                    <a href="request-quote.html?contractor=${contractor.id}" class="btn btn-outline-primary btn-sm flex-fill">Request Quote</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            // Re-attach favorite button event listeners
            attachFavoriteListeners();
        }

        function generateStars(rating) {
            const fullStars = Math.floor(rating);
            const hasHalfStar = rating % 1 >= 0.5;
            let stars = '';

            for (let i = 0; i < fullStars; i++) {
                stars += '<i class="fa fa-star"></i>';
            }

            if (hasHalfStar) {
                stars += '<i class="fa fa-star-half-alt"></i>';
            }

            const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
            for (let i = 0; i < emptyStars; i++) {
                stars += '<i class="far fa-star"></i>';
            }

            return stars;
        }

        function attachFavoriteListeners() {
            document.querySelectorAll('.favorite-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    toggleFavorite(this.dataset.contractor, this);
                });
            });
        }

        function toggleFavorite(contractorId, button) {
            const index = favorites.indexOf(contractorId);

            if (index > -1) {
                // Remove from favorites
                favorites.splice(index, 1);
                button.classList.remove('favorited', 'btn-danger');
                button.classList.add('btn-outline-danger');
            } else {
                // Add to favorites
                favorites.push(contractorId);
                button.classList.add('favorited', 'btn-danger');
                button.classList.remove('btn-outline-danger');
            }

            // Save to localStorage
            localStorage.setItem('favorites', JSON.stringify(favorites));

            // Show feedback
            showToast(index > -1 ? 'Removed from favorites' : 'Added to favorites');
        }

        function updateFavoriteButtons() {
            document.querySelectorAll('.favorite-btn').forEach(btn => {
                const contractorId = btn.dataset.contractor;
                if (favorites.includes(contractorId)) {
                    btn.classList.add('favorited', 'btn-danger');
                    btn.classList.remove('btn-outline-danger');
                }
            });

            attachFavoriteListeners();
        }

        function showToast(message) {
            // Simple toast notification
            const toast = document.createElement('div');
            toast.className = 'toast-notification';
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--accent-orange);
                color: white;
                padding: 12px 20px;
                border-radius: 5px;
                z-index: 9999;
                animation: slideIn 0.3s ease;
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => toast.remove(), 300);
            }, 2000);
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            attachFavoriteListeners();
        });
    </script>

    <style>
        .contractor-card {
            transition: transform 0.3s ease;
        }

        .contractor-card:hover {
            transform: translateY(-2px);
        }

        .favorite-btn {
            transition: all 0.3s ease;
        }

        .favorite-btn.favorited {
            background-color: #dc3545 !important;
            border-color: #dc3545 !important;
            color: white !important;
        }

        .sticky-top {
            position: sticky;
            top: 100px;
            z-index: 1020;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        @media (max-width: 768px) {
            .sticky-top {
                position: relative;
                top: auto;
            }
        }
    </style>
</body>

</html>
