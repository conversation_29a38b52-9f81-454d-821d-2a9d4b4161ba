<?php
/**
 * Brick & Click Database Configuration
 * PHP Database Connection and Configuration Examples
 */

// Database Configuration
class DatabaseConfig {
    // Database connection parameters
    private const DB_HOST = 'localhost';
    private const DB_NAME = 'brick_click_db';
    private const DB_USER = 'brick_click_user';
    private const DB_PASS = 'your_secure_password_here';
    private const DB_CHARSET = 'utf8mb4';
    
    // Connection options
    private const DB_OPTIONS = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ];
    
    private static $instance = null;
    private $connection = null;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . self::DB_HOST . ";dbname=" . self::DB_NAME . ";charset=" . self::DB_CHARSET;
            $this->connection = new PDO($dsn, self::DB_USER, self::DB_PASS, self::DB_OPTIONS);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed");
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    // Prevent cloning
    private function __clone() {}
    
    // Prevent unserialization
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

// Database Helper Class
class DatabaseHelper {
    private $db;
    
    public function __construct() {
        $this->db = DatabaseConfig::getInstance()->getConnection();
    }
    
    /**
     * Execute a prepared statement with parameters
     */
    public function execute($sql, $params = []) {
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database query failed: " . $e->getMessage());
            throw new Exception("Database operation failed");
        }
    }
    
    /**
     * Fetch a single row
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * Fetch all rows
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * Insert a record and return the last insert ID
     */
    public function insert($sql, $params = []) {
        $this->execute($sql, $params);
        return $this->db->lastInsertId();
    }
    
    /**
     * Update records and return affected row count
     */
    public function update($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * Delete records and return affected row count
     */
    public function delete($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->db->beginTransaction();
    }
    
    /**
     * Commit transaction
     */
    public function commit() {
        return $this->db->commit();
    }
    
    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->db->rollback();
    }
}

// Example Usage Classes

/**
 * User Management Class
 */
class UserManager {
    private $db;
    
    public function __construct() {
        $this->db = new DatabaseHelper();
    }
    
    /**
     * Create a new user
     */
    public function createUser($userData) {
        try {
            $this->db->beginTransaction();
            
            // Insert into users table
            $userSql = "INSERT INTO users (email, password_hash, user_type, first_name, last_name, phone, preferred_language) 
                       VALUES (:email, :password_hash, :user_type, :first_name, :last_name, :phone, :preferred_language)";
            
            $userId = $this->db->insert($userSql, [
                ':email' => $userData['email'],
                ':password_hash' => password_hash($userData['password'], PASSWORD_DEFAULT),
                ':user_type' => $userData['user_type'],
                ':first_name' => $userData['first_name'],
                ':last_name' => $userData['last_name'],
                ':phone' => $userData['phone'] ?? null,
                ':preferred_language' => $userData['preferred_language'] ?? 'en'
            ]);
            
            // Insert profile based on user type
            if ($userData['user_type'] === 'customer') {
                $profileSql = "INSERT INTO customer_profiles (user_id, district, address) 
                              VALUES (:user_id, :district, :address)";
                $this->db->insert($profileSql, [
                    ':user_id' => $userId,
                    ':district' => $userData['district'],
                    ':address' => $userData['address'] ?? null
                ]);
            } elseif ($userData['user_type'] === 'contractor') {
                $profileSql = "INSERT INTO contractor_profiles (user_id, business_name, years_experience, cida_registration_number, cida_grade, business_address, description) 
                              VALUES (:user_id, :business_name, :years_experience, :cida_registration_number, :cida_grade, :business_address, :description)";
                $this->db->insert($profileSql, [
                    ':user_id' => $userId,
                    ':business_name' => $userData['business_name'],
                    ':years_experience' => $userData['years_experience'],
                    ':cida_registration_number' => $userData['cida_registration_number'],
                    ':cida_grade' => $userData['cida_grade'],
                    ':business_address' => $userData['business_address'] ?? null,
                    ':description' => $userData['description'] ?? null
                ]);
            }
            
            $this->db->commit();
            return $userId;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Authenticate user
     */
    public function authenticateUser($email, $password) {
        $sql = "SELECT id, email, password_hash, user_type, first_name, last_name, status 
                FROM users WHERE email = :email AND status = 'active'";
        
        $user = $this->db->fetchOne($sql, [':email' => $email]);
        
        if ($user && password_verify($password, $user['password_hash'])) {
            // Update last login
            $this->db->update("UPDATE users SET last_login = NOW() WHERE id = :id", [':id' => $user['id']]);
            
            unset($user['password_hash']); // Remove password hash from returned data
            return $user;
        }
        
        return false;
    }
    
    /**
     * Get user profile with additional data
     */
    public function getUserProfile($userId) {
        $sql = "SELECT u.*, 
                CASE 
                    WHEN u.user_type = 'customer' THEN cp.district
                    WHEN u.user_type = 'contractor' THEN ctp.business_name
                    ELSE NULL
                END as additional_info
                FROM users u
                LEFT JOIN customer_profiles cp ON u.id = cp.user_id AND u.user_type = 'customer'
                LEFT JOIN contractor_profiles ctp ON u.id = ctp.user_id AND u.user_type = 'contractor'
                WHERE u.id = :user_id";
        
        return $this->db->fetchOne($sql, [':user_id' => $userId]);
    }
}

/**
 * Contractor Search Class
 */
class ContractorSearch {
    private $db;
    
    public function __construct() {
        $this->db = new DatabaseHelper();
    }
    
    /**
     * Search contractors with filters
     */
    public function searchContractors($filters = [], $page = 1, $limit = 12) {
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT * FROM contractor_search_view WHERE verification_status = 'approved'";
        $params = [];
        
        // Apply filters
        if (!empty($filters['search'])) {
            $sql .= " AND (business_name LIKE :search OR CONCAT(first_name, ' ', last_name) LIKE :search)";
            $params[':search'] = '%' . $filters['search'] . '%';
        }
        
        if (!empty($filters['district'])) {
            $sql .= " AND contractor_id IN (SELECT contractor_id FROM contractor_service_areas csa 
                     JOIN districts d ON csa.district_id = d.id WHERE d.name = :district)";
            $params[':district'] = $filters['district'];
        }
        
        if (!empty($filters['service_category'])) {
            $sql .= " AND contractor_id IN (SELECT contractor_id FROM contractor_services cs 
                     JOIN service_categories sc ON cs.service_category_id = sc.id WHERE sc.name = :service_category)";
            $params[':service_category'] = $filters['service_category'];
        }
        
        if (!empty($filters['min_rating'])) {
            $sql .= " AND average_rating >= :min_rating";
            $params[':min_rating'] = $filters['min_rating'];
        }
        
        if (!empty($filters['cida_grade'])) {
            $sql .= " AND cida_grade = :cida_grade";
            $params[':cida_grade'] = $filters['cida_grade'];
        }
        
        // Sorting
        $sortBy = $filters['sort_by'] ?? 'rating';
        switch ($sortBy) {
            case 'rating':
                $sql .= " ORDER BY average_rating DESC";
                break;
            case 'reviews':
                $sql .= " ORDER BY total_reviews DESC";
                break;
            case 'experience':
                $sql .= " ORDER BY years_experience DESC";
                break;
            case 'name':
                $sql .= " ORDER BY business_name ASC";
                break;
            default:
                $sql .= " ORDER BY average_rating DESC";
        }
        
        // Pagination
        $sql .= " LIMIT :limit OFFSET :offset";
        $params[':limit'] = $limit;
        $params[':offset'] = $offset;
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get contractor details
     */
    public function getContractorDetails($contractorId) {
        $sql = "SELECT cp.*, u.first_name, u.last_name, u.email, u.phone, u.profile_image,
                GROUP_CONCAT(DISTINCT sc.name) as services,
                GROUP_CONCAT(DISTINCT d.name) as service_areas
                FROM contractor_profiles cp
                JOIN users u ON cp.user_id = u.id
                LEFT JOIN contractor_services cs ON cp.id = cs.contractor_id
                LEFT JOIN service_categories sc ON cs.service_category_id = sc.id
                LEFT JOIN contractor_service_areas csa ON cp.id = csa.contractor_id
                LEFT JOIN districts d ON csa.district_id = d.id
                WHERE cp.id = :contractor_id AND cp.verification_status = 'approved'
                GROUP BY cp.id";
        
        return $this->db->fetchOne($sql, [':contractor_id' => $contractorId]);
    }
}

/**
 * Quote Management Class
 */
class QuoteManager {
    private $db;
    
    public function __construct() {
        $this->db = new DatabaseHelper();
    }
    
    /**
     * Create a quote request
     */
    public function createQuoteRequest($quoteData) {
        $sql = "INSERT INTO quote_requests (customer_id, title, description, service_category_id, 
                project_type, budget_min, budget_max, project_location, district_id, 
                project_start_date, floor_area, additional_requirements) 
                VALUES (:customer_id, :title, :description, :service_category_id, 
                :project_type, :budget_min, :budget_max, :project_location, :district_id, 
                :project_start_date, :floor_area, :additional_requirements)";
        
        return $this->db->insert($sql, $quoteData);
    }
    
    /**
     * Get quote requests for a customer
     */
    public function getCustomerQuotes($customerId) {
        $sql = "SELECT qr.*, sc.name as service_category, d.name as district_name,
                COUNT(qresp.id) as response_count
                FROM quote_requests qr
                JOIN service_categories sc ON qr.service_category_id = sc.id
                JOIN districts d ON qr.district_id = d.id
                LEFT JOIN quote_responses qresp ON qr.id = qresp.quote_request_id
                WHERE qr.customer_id = :customer_id
                GROUP BY qr.id
                ORDER BY qr.created_at DESC";
        
        return $this->db->fetchAll($sql, [':customer_id' => $customerId]);
    }
    
    /**
     * Submit quote response
     */
    public function submitQuoteResponse($responseData) {
        $sql = "INSERT INTO quote_responses (quote_request_id, contractor_id, quoted_amount, 
                estimated_duration_weeks, description, terms_and_conditions, warranty_period_months) 
                VALUES (:quote_request_id, :contractor_id, :quoted_amount, 
                :estimated_duration_weeks, :description, :terms_and_conditions, :warranty_period_months)";
        
        return $this->db->insert($sql, $responseData);
    }
}

// Environment-specific configuration
if (file_exists(__DIR__ . '/.env')) {
    $env = parse_ini_file(__DIR__ . '/.env');
    
    // Override default values with environment variables
    if (isset($env['DB_HOST'])) {
        // Update DatabaseConfig constants with environment values
        // Note: In production, use a proper configuration management system
    }
}

// Error handling and logging
function logDatabaseError($message, $context = []) {
    $logMessage = date('Y-m-d H:i:s') . " - " . $message;
    if (!empty($context)) {
        $logMessage .= " - Context: " . json_encode($context);
    }
    error_log($logMessage, 3, __DIR__ . '/logs/database_errors.log');
}

// Security functions
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

?>
