-- Brick & Click Database Schema
-- Sri Lanka Construction Contractor Platform
-- Created: 2025-01-19

-- Set character set and collation for Sri Lankan languages support
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- Create database
CREATE DATABASE IF NOT EXISTS brick_click_db
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE brick_click_db;

-- =============================================
-- USER MANAGEMENT TABLES
-- =============================================

-- Users table (base table for all user types)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    user_type ENUM('customer', 'contractor', 'admin') NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    profile_image VARCHAR(255),
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires DATETIME,
    status ENUM('active', 'inactive', 'suspended', 'pending_verification') DEFAULT 'pending_verification',
    preferred_language ENUM('en', 'si', 'ta') DEFAULT 'en',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,

    INDEX idx_email (email),
    INDEX idx_user_type (user_type),
    INDEX idx_status (status)
);

-- Customer profiles
CREATE TABLE customer_profiles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    district VARCHAR(50) NOT NULL,
    address TEXT,
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_district (district)
);

-- Contractor profiles
CREATE TABLE contractor_profiles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    business_name VARCHAR(255) NOT NULL,
    business_registration_number VARCHAR(100),
    years_experience INT NOT NULL,
    cida_registration_number VARCHAR(100) NOT NULL,
    cida_grade ENUM('C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'C7', 'C8', 'C9', 'C10') NOT NULL,
    cida_document_path VARCHAR(255),
    business_license_path VARCHAR(255),
    verification_status ENUM('pending', 'approved', 'rejected', 'suspended') DEFAULT 'pending',
    verification_date TIMESTAMP NULL,
    verified_by INT NULL,
    rejection_reason TEXT,
    business_address TEXT,
    website_url VARCHAR(255),
    description TEXT,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INT DEFAULT 0,
    total_projects INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_verification_status (verification_status),
    INDEX idx_cida_grade (cida_grade),
    INDEX idx_average_rating (average_rating),
    UNIQUE KEY unique_cida (cida_registration_number)
);

-- Admin profiles
CREATE TABLE admin_profiles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
    permissions JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- =============================================
-- SERVICE AND LOCATION TABLES
-- =============================================

-- Service categories
CREATE TABLE service_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    name_si VARCHAR(100),
    name_ta VARCHAR(100),
    description TEXT,
    icon VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_active (is_active),
    INDEX idx_sort_order (sort_order)
);

-- Districts (Sri Lankan districts)
CREATE TABLE districts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    name_si VARCHAR(100),
    name_ta VARCHAR(100),
    province VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,

    INDEX idx_province (province),
    INDEX idx_active (is_active)
);

-- Contractor services (many-to-many relationship)
CREATE TABLE contractor_services (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contractor_id INT NOT NULL,
    service_category_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (contractor_id) REFERENCES contractor_profiles(id) ON DELETE CASCADE,
    FOREIGN KEY (service_category_id) REFERENCES service_categories(id) ON DELETE CASCADE,
    UNIQUE KEY unique_contractor_service (contractor_id, service_category_id)
);

-- Contractor service areas (many-to-many relationship)
CREATE TABLE contractor_service_areas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contractor_id INT NOT NULL,
    district_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (contractor_id) REFERENCES contractor_profiles(id) ON DELETE CASCADE,
    FOREIGN KEY (district_id) REFERENCES districts(id) ON DELETE CASCADE,
    UNIQUE KEY unique_contractor_district (contractor_id, district_id)
);

-- =============================================
-- QUOTE MANAGEMENT TABLES
-- =============================================

-- Quote requests
CREATE TABLE quote_requests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    service_category_id INT NOT NULL,
    project_type ENUM('new_construction', 'renovation', 'extension', 'repair', 'other') NOT NULL,
    budget_min DECIMAL(12,2),
    budget_max DECIMAL(12,2),
    project_location VARCHAR(255) NOT NULL,
    district_id INT NOT NULL,
    project_start_date DATE,
    project_duration_weeks INT,
    floor_area INT,
    number_of_floors INT,
    additional_requirements TEXT,
    attachments JSON, -- Store file paths as JSON array
    status ENUM('open', 'closed', 'cancelled') DEFAULT 'open',
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_category_id) REFERENCES service_categories(id),
    FOREIGN KEY (district_id) REFERENCES districts(id),
    INDEX idx_customer (customer_id),
    INDEX idx_status (status),
    INDEX idx_service_category (service_category_id),
    INDEX idx_district (district_id),
    INDEX idx_created_at (created_at)
);

-- Quote responses from contractors
CREATE TABLE quote_responses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    quote_request_id INT NOT NULL,
    contractor_id INT NOT NULL,
    quoted_amount DECIMAL(12,2) NOT NULL,
    estimated_duration_weeks INT,
    description TEXT NOT NULL,
    terms_and_conditions TEXT,
    materials_included BOOLEAN DEFAULT TRUE,
    labor_included BOOLEAN DEFAULT TRUE,
    warranty_period_months INT DEFAULT 12,
    payment_terms TEXT,
    attachments JSON, -- Store file paths as JSON array
    status ENUM('pending', 'accepted', 'rejected', 'withdrawn') DEFAULT 'pending',
    valid_until TIMESTAMP NULL,
    responded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (quote_request_id) REFERENCES quote_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (contractor_id) REFERENCES contractor_profiles(id) ON DELETE CASCADE,
    INDEX idx_quote_request (quote_request_id),
    INDEX idx_contractor (contractor_id),
    INDEX idx_status (status),
    UNIQUE KEY unique_contractor_quote (quote_request_id, contractor_id)
);

-- =============================================
-- PROJECT MANAGEMENT TABLES
-- =============================================

-- Projects (accepted quotes become projects)
CREATE TABLE projects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    quote_response_id INT NOT NULL,
    customer_id INT NOT NULL,
    contractor_id INT NOT NULL,
    project_name VARCHAR(255) NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    down_payment_amount DECIMAL(12,2),
    down_payment_status ENUM('pending', 'paid', 'failed') DEFAULT 'pending',
    down_payment_date TIMESTAMP NULL,
    status ENUM('planning', 'in_progress', 'on_hold', 'completed', 'cancelled') DEFAULT 'planning',
    start_date DATE,
    expected_completion_date DATE,
    actual_completion_date DATE,
    progress_percentage INT DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (quote_response_id) REFERENCES quote_responses(id),
    FOREIGN KEY (customer_id) REFERENCES users(id),
    FOREIGN KEY (contractor_id) REFERENCES contractor_profiles(id),
    INDEX idx_customer (customer_id),
    INDEX idx_contractor (contractor_id),
    INDEX idx_status (status)
);

-- Project milestones and updates
CREATE TABLE project_updates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    update_type ENUM('milestone', 'progress', 'issue', 'completion') NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    progress_percentage INT,
    images JSON, -- Store image paths as JSON array
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_project (project_id),
    INDEX idx_created_at (created_at)
);

-- =============================================
-- PAYMENT MANAGEMENT TABLES
-- =============================================

-- Payment schedules
CREATE TABLE payment_schedules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    milestone_name VARCHAR(255) NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    due_date DATE NOT NULL,
    status ENUM('pending', 'paid', 'overdue', 'cancelled') DEFAULT 'pending',
    payment_date TIMESTAMP NULL,
    payment_method VARCHAR(50),
    transaction_reference VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    INDEX idx_project (project_id),
    INDEX idx_status (status),
    INDEX idx_due_date (due_date)
);

-- Payment transactions
CREATE TABLE payment_transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    payment_schedule_id INT,
    customer_id INT NOT NULL,
    contractor_id INT NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    payment_type ENUM('down_payment', 'milestone', 'final', 'refund') NOT NULL,
    payment_method ENUM('bank_transfer', 'card', 'cash', 'cheque') NOT NULL,
    transaction_reference VARCHAR(255),
    gateway_transaction_id VARCHAR(255),
    status ENUM('pending', 'processing', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (payment_schedule_id) REFERENCES payment_schedules(id),
    FOREIGN KEY (customer_id) REFERENCES users(id),
    FOREIGN KEY (contractor_id) REFERENCES contractor_profiles(id),
    INDEX idx_project (project_id),
    INDEX idx_customer (customer_id),
    INDEX idx_contractor (contractor_id),
    INDEX idx_status (status)
);

-- =============================================
-- REVIEW AND RATING TABLES
-- =============================================

-- Reviews and ratings
CREATE TABLE reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    customer_id INT NOT NULL,
    contractor_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(255),
    review_text TEXT,
    quality_rating INT CHECK (quality_rating >= 1 AND quality_rating <= 5),
    timeliness_rating INT CHECK (timeliness_rating >= 1 AND timeliness_rating <= 5),
    communication_rating INT CHECK (communication_rating >= 1 AND communication_rating <= 5),
    value_rating INT CHECK (value_rating >= 1 AND value_rating <= 5),
    images JSON, -- Store image paths as JSON array
    is_verified BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    status ENUM('pending', 'approved', 'rejected', 'hidden') DEFAULT 'pending',
    moderated_by INT NULL,
    moderated_at TIMESTAMP NULL,
    moderation_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (customer_id) REFERENCES users(id),
    FOREIGN KEY (contractor_id) REFERENCES contractor_profiles(id),
    FOREIGN KEY (moderated_by) REFERENCES users(id),
    INDEX idx_contractor (contractor_id),
    INDEX idx_customer (customer_id),
    INDEX idx_rating (rating),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    UNIQUE KEY unique_project_review (project_id, customer_id)
);

-- Review responses from contractors
CREATE TABLE review_responses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    review_id INT NOT NULL,
    contractor_id INT NOT NULL,
    response_text TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (review_id) REFERENCES reviews(id) ON DELETE CASCADE,
    FOREIGN KEY (contractor_id) REFERENCES contractor_profiles(id) ON DELETE CASCADE,
    UNIQUE KEY unique_review_response (review_id)
);

-- =============================================
-- PORTFOLIO AND MEDIA TABLES
-- =============================================

-- Contractor portfolios
CREATE TABLE contractor_portfolios (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contractor_id INT NOT NULL,
    project_name VARCHAR(255) NOT NULL,
    project_description TEXT,
    project_type VARCHAR(100),
    completion_date DATE,
    project_value DECIMAL(12,2),
    client_name VARCHAR(255),
    location VARCHAR(255),
    duration_weeks INT,
    featured_image VARCHAR(255),
    is_featured BOOLEAN DEFAULT FALSE,
    display_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (contractor_id) REFERENCES contractor_profiles(id) ON DELETE CASCADE,
    INDEX idx_contractor (contractor_id),
    INDEX idx_featured (is_featured),
    INDEX idx_status (status),
    INDEX idx_display_order (display_order)
);

-- Portfolio images
CREATE TABLE portfolio_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    portfolio_id INT NOT NULL,
    image_path VARCHAR(255) NOT NULL,
    image_caption VARCHAR(255),
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (portfolio_id) REFERENCES contractor_portfolios(id) ON DELETE CASCADE,
    INDEX idx_portfolio (portfolio_id),
    INDEX idx_display_order (display_order)
);

-- =============================================
-- FAVORITES AND NOTIFICATIONS TABLES
-- =============================================

-- Customer favorites
CREATE TABLE customer_favorites (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    contractor_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (contractor_id) REFERENCES contractor_profiles(id) ON DELETE CASCADE,
    UNIQUE KEY unique_customer_contractor (customer_id, contractor_id),
    INDEX idx_customer (customer_id),
    INDEX idx_contractor (contractor_id)
);

-- Notifications
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    type ENUM('quote_received', 'quote_accepted', 'quote_rejected', 'project_update', 'payment_due', 'payment_received', 'review_received', 'verification_status', 'system') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    related_id INT, -- ID of related entity (quote, project, etc.)
    related_type VARCHAR(50), -- Type of related entity
    is_read BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_type (type),
    INDEX idx_read (is_read),
    INDEX idx_created_at (created_at)
);

-- =============================================
-- SYSTEM AND CONFIGURATION TABLES
-- =============================================

-- System settings
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_key (setting_key),
    INDEX idx_public (is_public)
);

-- Activity logs
CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50),
    entity_id INT,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_created_at (created_at)
);

-- Reports and disputes
CREATE TABLE reports (
    id INT PRIMARY KEY AUTO_INCREMENT,
    reporter_id INT NOT NULL,
    reported_user_id INT,
    reported_content_type ENUM('contractor', 'review', 'project', 'other') NOT NULL,
    reported_content_id INT,
    reason ENUM('inappropriate_content', 'fake_profile', 'poor_service', 'fraud', 'spam', 'other') NOT NULL,
    description TEXT NOT NULL,
    status ENUM('pending', 'investigating', 'resolved', 'dismissed') DEFAULT 'pending',
    assigned_to INT,
    resolution_notes TEXT,
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (reporter_id) REFERENCES users(id),
    FOREIGN KEY (reported_user_id) REFERENCES users(id),
    FOREIGN KEY (assigned_to) REFERENCES users(id),
    INDEX idx_reporter (reporter_id),
    INDEX idx_reported_user (reported_user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Cost estimation data
CREATE TABLE cost_estimates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    project_type VARCHAR(50) NOT NULL,
    building_type VARCHAR(50) NOT NULL,
    floor_area INT NOT NULL,
    floors INT NOT NULL,
    district VARCHAR(50) NOT NULL,
    bedrooms INT,
    bathrooms INT,
    accessibility VARCHAR(20),
    additional_features JSON,
    estimated_min_cost DECIMAL(12,2) NOT NULL,
    estimated_max_cost DECIMAL(12,2) NOT NULL,
    estimated_avg_cost DECIMAL(12,2) NOT NULL,
    session_id VARCHAR(255),
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_project_type (project_type),
    INDEX idx_district (district),
    INDEX idx_created_at (created_at)
);

-- =============================================
-- INSERT INITIAL DATA
-- =============================================

-- Insert service categories
INSERT INTO service_categories (name, name_si, name_ta, description, icon) VALUES
('Residential Construction', 'නේවාසික ඉදිකිරීම්', 'குடியிருப்பு கட்டுமானம்', 'New house construction and residential buildings', 'fa-home'),
('Commercial Construction', 'වාණිජ ඉදිකිරීම්', 'வணிக கட்டுமானம்', 'Office buildings, shops, and commercial structures', 'fa-building'),
('Renovation & Remodeling', 'අලුත්වැඩියා සහ ප්‍රතිසංස්කරණ', 'புதுப்பித்தல் மற்றும் மறுவடிவமைப்பு', 'Home and building renovation services', 'fa-tools'),
('Electrical Work', 'විදුලි වැඩ', 'மின்சார வேலை', 'Electrical installations and repairs', 'fa-bolt'),
('Plumbing', 'ජල නල සේවා', 'குழாய் வேலை', 'Plumbing installations and repairs', 'fa-wrench'),
('Interior Design', 'අභ්‍යන්තර සැලසුම්', 'உள்துறை வடிவமைப்பு', 'Interior design and decoration services', 'fa-paint-brush');

-- Insert Sri Lankan districts
INSERT INTO districts (name, name_si, name_ta, province) VALUES
('Colombo', 'කොළඹ', 'கொழும்பு', 'Western'),
('Gampaha', 'ගම්පහ', 'கம்பஹா', 'Western'),
('Kalutara', 'කළුතර', 'களுத்துறை', 'Western'),
('Kandy', 'මහනුවර', 'கண்டி', 'Central'),
('Matale', 'මාතලේ', 'மாத்தளை', 'Central'),
('Nuwara Eliya', 'නුවරඑළිය', 'நுவரேலியா', 'Central'),
('Galle', 'ගාල්ල', 'காலி', 'Southern'),
('Matara', 'මාතර', 'மாத்தறை', 'Southern'),
('Hambantota', 'හම්බන්තොට', 'அம்பாந்தோட்டை', 'Southern'),
('Jaffna', 'යාපනය', 'யாழ்ப்பாணம்', 'Northern'),
('Kilinochchi', 'කිලිනොච්චි', 'கிளிநொச்சி', 'Northern'),
('Mannar', 'මන්නාරම', 'மன்னார்', 'Northern'),
('Vavuniya', 'වවුනියාව', 'வவுனியா', 'Northern'),
('Mullaitivu', 'මුලතිව්', 'முல்லைத்தீவு', 'Northern'),
('Batticaloa', 'මඩකලපුව', 'மட்டக்களப்பு', 'Eastern'),
('Ampara', 'අම්පාර', 'அம்பாறை', 'Eastern'),
('Trincomalee', 'ත්‍රිකුණාමලය', 'திருகோணமலை', 'Eastern'),
('Kurunegala', 'කුරුණෑගල', 'குருணாகல்', 'North Western'),
('Puttalam', 'පුත්තලම', 'புத்தளம்', 'North Western'),
('Anuradhapura', 'අනුරාධපුරය', 'அனுராதபுரம்', 'North Central'),
('Polonnaruwa', 'පොළොන්නරුව', 'பொலன்னறுவை', 'North Central'),
('Badulla', 'බදුල්ල', 'பதுளை', 'Uva'),
('Moneragala', 'මොණරාගල', 'மொணராகலை', 'Uva'),
('Ratnapura', 'රත්නපුර', 'இரத்தினபுரி', 'Sabaragamuwa'),
('Kegalle', 'කෑගල්ල', 'கேகாலை', 'Sabaragamuwa');

-- Insert system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('site_name', 'Brick & Click', 'string', 'Website name', TRUE),
('site_description', 'Sri Lanka\'s Verified Construction Contractor Platform', 'string', 'Website description', TRUE),
('default_language', 'en', 'string', 'Default system language', TRUE),
('supported_languages', '["en", "si", "ta"]', 'json', 'Supported languages', TRUE),
('max_file_upload_size', '5242880', 'number', 'Maximum file upload size in bytes (5MB)', FALSE),
('allowed_file_types', '["jpg", "jpeg", "png", "pdf"]', 'json', 'Allowed file upload types', FALSE),
('quote_expiry_days', '30', 'number', 'Default quote expiry in days', FALSE),
('min_contractor_experience', '1', 'number', 'Minimum contractor experience in years', FALSE),
('review_moderation_required', 'true', 'boolean', 'Whether reviews require moderation', FALSE),
('email_verification_required', 'true', 'boolean', 'Whether email verification is required', FALSE);

-- Create admin user (password: admin123 - should be changed in production)
INSERT INTO users (email, password_hash, user_type, first_name, last_name, status, email_verified) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'System', 'Administrator', 'active', TRUE);

INSERT INTO admin_profiles (user_id, role, permissions) VALUES
(1, 'super_admin', '{"all": true}');

-- =============================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =============================================

-- Additional composite indexes for common queries
CREATE INDEX idx_contractor_verification_rating ON contractor_profiles(verification_status, average_rating DESC);
CREATE INDEX idx_quote_requests_open ON quote_requests(status, created_at DESC);
CREATE INDEX idx_projects_active ON projects(status, updated_at DESC);
CREATE INDEX idx_reviews_approved_rating ON reviews(status, rating DESC, created_at DESC);
CREATE INDEX idx_notifications_unread ON notifications(user_id, is_read, created_at DESC);

-- =============================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =============================================

-- Trigger to update contractor average rating when a new review is added
DELIMITER //
CREATE TRIGGER update_contractor_rating_after_review
AFTER INSERT ON reviews
FOR EACH ROW
BEGIN
    IF NEW.status = 'approved' THEN
        UPDATE contractor_profiles
        SET
            average_rating = (
                SELECT AVG(rating)
                FROM reviews
                WHERE contractor_id = NEW.contractor_id AND status = 'approved'
            ),
            total_reviews = (
                SELECT COUNT(*)
                FROM reviews
                WHERE contractor_id = NEW.contractor_id AND status = 'approved'
            )
        WHERE id = NEW.contractor_id;
    END IF;
END//

-- Trigger to update contractor rating when review status changes
CREATE TRIGGER update_contractor_rating_after_review_update
AFTER UPDATE ON reviews
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        UPDATE contractor_profiles
        SET
            average_rating = (
                SELECT COALESCE(AVG(rating), 0)
                FROM reviews
                WHERE contractor_id = NEW.contractor_id AND status = 'approved'
            ),
            total_reviews = (
                SELECT COUNT(*)
                FROM reviews
                WHERE contractor_id = NEW.contractor_id AND status = 'approved'
            )
        WHERE id = NEW.contractor_id;
    END IF;
END//

-- Trigger to create notification when quote response is received
CREATE TRIGGER create_quote_response_notification
AFTER INSERT ON quote_responses
FOR EACH ROW
BEGIN
    DECLARE customer_id INT;
    DECLARE contractor_name VARCHAR(255);

    SELECT qr.customer_id INTO customer_id
    FROM quote_requests qr
    WHERE qr.id = NEW.quote_request_id;

    SELECT CONCAT(u.first_name, ' ', u.last_name) INTO contractor_name
    FROM users u
    JOIN contractor_profiles cp ON u.id = cp.user_id
    WHERE cp.id = NEW.contractor_id;

    INSERT INTO notifications (user_id, type, title, message, related_id, related_type, action_url)
    VALUES (
        customer_id,
        'quote_received',
        'New Quote Received',
        CONCAT('You received a new quote from ', contractor_name),
        NEW.id,
        'quote_response',
        CONCAT('/my-quotes.html?quote=', NEW.quote_request_id)
    );
END//

-- Trigger to create notification when quote is accepted
CREATE TRIGGER create_quote_accepted_notification
AFTER UPDATE ON quote_responses
FOR EACH ROW
BEGIN
    DECLARE contractor_user_id INT;
    DECLARE customer_name VARCHAR(255);

    IF OLD.status != 'accepted' AND NEW.status = 'accepted' THEN
        SELECT u.id INTO contractor_user_id
        FROM users u
        JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE cp.id = NEW.contractor_id;

        SELECT CONCAT(u.first_name, ' ', u.last_name) INTO customer_name
        FROM users u
        JOIN quote_requests qr ON u.id = qr.customer_id
        WHERE qr.id = NEW.quote_request_id;

        INSERT INTO notifications (user_id, type, title, message, related_id, related_type, action_url)
        VALUES (
            contractor_user_id,
            'quote_accepted',
            'Quote Accepted!',
            CONCAT('Your quote has been accepted by ', customer_name),
            NEW.id,
            'quote_response',
            '/contractor-dashboard.html'
        );
    END IF;
END//

DELIMITER ;

-- =============================================
-- VIEWS FOR COMMON QUERIES
-- =============================================

-- View for contractor search with all relevant information
CREATE VIEW contractor_search_view AS
SELECT
    cp.id as contractor_id,
    cp.user_id,
    u.first_name,
    u.last_name,
    cp.business_name,
    cp.years_experience,
    cp.cida_grade,
    cp.average_rating,
    cp.total_reviews,
    cp.verification_status,
    cp.description,
    GROUP_CONCAT(DISTINCT sc.name) as services,
    GROUP_CONCAT(DISTINCT d.name) as service_areas,
    u.profile_image,
    cp.created_at
FROM contractor_profiles cp
JOIN users u ON cp.user_id = u.id
LEFT JOIN contractor_services cs ON cp.id = cs.contractor_id
LEFT JOIN service_categories sc ON cs.service_category_id = sc.id
LEFT JOIN contractor_service_areas csa ON cp.id = csa.contractor_id
LEFT JOIN districts d ON csa.district_id = d.id
WHERE cp.verification_status = 'approved' AND u.status = 'active'
GROUP BY cp.id;

-- View for project dashboard
CREATE VIEW project_dashboard_view AS
SELECT
    p.id as project_id,
    p.project_name,
    p.status,
    p.progress_percentage,
    p.start_date,
    p.expected_completion_date,
    p.total_amount,
    CONCAT(cu.first_name, ' ', cu.last_name) as customer_name,
    cp.business_name as contractor_name,
    sc.name as service_category,
    p.created_at,
    p.updated_at
FROM projects p
JOIN users cu ON p.customer_id = cu.id
JOIN contractor_profiles cp ON p.contractor_id = cp.id
JOIN quote_responses qr ON p.quote_response_id = qr.id
JOIN quote_requests qreq ON qr.quote_request_id = qreq.id
JOIN service_categories sc ON qreq.service_category_id = sc.id;

-- View for admin dashboard statistics
CREATE VIEW admin_stats_view AS
SELECT
    (SELECT COUNT(*) FROM users WHERE user_type = 'customer' AND status = 'active') as total_customers,
    (SELECT COUNT(*) FROM contractor_profiles WHERE verification_status = 'approved') as verified_contractors,
    (SELECT COUNT(*) FROM contractor_profiles WHERE verification_status = 'pending') as pending_contractors,
    (SELECT COUNT(*) FROM quote_requests WHERE status = 'open') as open_quotes,
    (SELECT COUNT(*) FROM projects WHERE status IN ('planning', 'in_progress')) as active_projects,
    (SELECT COUNT(*) FROM reviews WHERE status = 'pending') as pending_reviews,
    (SELECT COUNT(*) FROM reports WHERE status = 'pending') as pending_reports,
    (SELECT SUM(total_amount) FROM projects WHERE status = 'completed') as total_project_value;

-- =============================================
-- STORED PROCEDURES
-- =============================================

-- Procedure to get contractor statistics
DELIMITER //
CREATE PROCEDURE GetContractorStats(IN contractor_id INT)
BEGIN
    SELECT
        cp.business_name,
        cp.average_rating,
        cp.total_reviews,
        cp.total_projects,
        COUNT(DISTINCT p.id) as active_projects,
        COUNT(DISTINCT cf.id) as total_favorites,
        AVG(r.rating) as recent_rating
    FROM contractor_profiles cp
    LEFT JOIN projects p ON cp.id = p.contractor_id AND p.status IN ('planning', 'in_progress')
    LEFT JOIN customer_favorites cf ON cp.id = cf.contractor_id
    LEFT JOIN reviews r ON cp.id = r.contractor_id AND r.created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
    WHERE cp.id = contractor_id
    GROUP BY cp.id;
END//

-- Procedure to search contractors with filters
CREATE PROCEDURE SearchContractors(
    IN search_term VARCHAR(255),
    IN service_category_id INT,
    IN district_id INT,
    IN min_rating DECIMAL(3,2),
    IN cida_grade VARCHAR(10),
    IN min_experience INT,
    IN sort_by VARCHAR(20),
    IN page_limit INT,
    IN page_offset INT
)
BEGIN
    SET @sql = 'SELECT * FROM contractor_search_view WHERE verification_status = "approved"';

    IF search_term IS NOT NULL AND search_term != '' THEN
        SET @sql = CONCAT(@sql, ' AND (business_name LIKE "%', search_term, '%" OR CONCAT(first_name, " ", last_name) LIKE "%', search_term, '%")');
    END IF;

    IF service_category_id IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' AND contractor_id IN (SELECT contractor_id FROM contractor_services WHERE service_category_id = ', service_category_id, ')');
    END IF;

    IF district_id IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' AND contractor_id IN (SELECT contractor_id FROM contractor_service_areas WHERE district_id = ', district_id, ')');
    END IF;

    IF min_rating IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' AND average_rating >= ', min_rating);
    END IF;

    IF cida_grade IS NOT NULL AND cida_grade != '' THEN
        SET @sql = CONCAT(@sql, ' AND cida_grade = "', cida_grade, '"');
    END IF;

    IF min_experience IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' AND years_experience >= ', min_experience);
    END IF;

    -- Add sorting
    IF sort_by = 'rating' THEN
        SET @sql = CONCAT(@sql, ' ORDER BY average_rating DESC');
    ELSEIF sort_by = 'reviews' THEN
        SET @sql = CONCAT(@sql, ' ORDER BY total_reviews DESC');
    ELSEIF sort_by = 'experience' THEN
        SET @sql = CONCAT(@sql, ' ORDER BY years_experience DESC');
    ELSEIF sort_by = 'name' THEN
        SET @sql = CONCAT(@sql, ' ORDER BY business_name ASC');
    ELSE
        SET @sql = CONCAT(@sql, ' ORDER BY average_rating DESC');
    END IF;

    -- Add pagination
    IF page_limit IS NOT NULL THEN
        SET @sql = CONCAT(@sql, ' LIMIT ', page_limit);
        IF page_offset IS NOT NULL THEN
            SET @sql = CONCAT(@sql, ' OFFSET ', page_offset);
        END IF;
    END IF;

    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END//

-- Procedure to calculate cost estimate
CREATE PROCEDURE CalculateCostEstimate(
    IN project_type VARCHAR(50),
    IN building_type VARCHAR(50),
    IN floor_area INT,
    IN floors INT,
    IN district VARCHAR(50),
    IN additional_features JSON,
    OUT min_cost DECIMAL(12,2),
    OUT max_cost DECIMAL(12,2),
    OUT avg_cost DECIMAL(12,2)
)
BEGIN
    DECLARE base_min_rate DECIMAL(8,2);
    DECLARE base_max_rate DECIMAL(8,2);
    DECLARE location_multiplier DECIMAL(3,2) DEFAULT 1.0;
    DECLARE floor_multiplier DECIMAL(3,2) DEFAULT 1.0;
    DECLARE features_cost DECIMAL(12,2) DEFAULT 0;

    -- Set base rates based on project and building type
    CASE
        WHEN project_type = 'new-house' AND building_type = 'basic' THEN
            SET base_min_rate = 8000, base_max_rate = 12000;
        WHEN project_type = 'new-house' AND building_type = 'medium' THEN
            SET base_min_rate = 12000, base_max_rate = 18000;
        WHEN project_type = 'new-house' AND building_type = 'luxury' THEN
            SET base_min_rate = 18000, base_max_rate = 30000;
        WHEN project_type = 'renovation' AND building_type = 'basic' THEN
            SET base_min_rate = 4000, base_max_rate = 7000;
        WHEN project_type = 'renovation' AND building_type = 'medium' THEN
            SET base_min_rate = 7000, base_max_rate = 12000;
        WHEN project_type = 'renovation' AND building_type = 'luxury' THEN
            SET base_min_rate = 12000, base_max_rate = 20000;
        ELSE
            SET base_min_rate = 8000, base_max_rate = 15000;
    END CASE;

    -- Set location multiplier
    CASE district
        WHEN 'colombo' THEN SET location_multiplier = 1.2;
        WHEN 'gampaha' THEN SET location_multiplier = 1.1;
        WHEN 'kalutara' THEN SET location_multiplier = 1.0;
        WHEN 'kandy' THEN SET location_multiplier = 0.95;
        ELSE SET location_multiplier = 0.9;
    END CASE;

    -- Set floor multiplier
    CASE floors
        WHEN 1 THEN SET floor_multiplier = 1.0;
        WHEN 2 THEN SET floor_multiplier = 1.15;
        WHEN 3 THEN SET floor_multiplier = 1.25;
        ELSE SET floor_multiplier = 1.35;
    END CASE;

    -- Calculate base costs
    SET min_cost = base_min_rate * floor_area * location_multiplier * floor_multiplier;
    SET max_cost = base_max_rate * floor_area * location_multiplier * floor_multiplier;

    -- Add features cost (simplified)
    IF JSON_CONTAINS(additional_features, '"pool"') THEN
        SET features_cost = features_cost + 1500000;
    END IF;
    IF JSON_CONTAINS(additional_features, '"garage"') THEN
        SET features_cost = features_cost + 300000;
    END IF;
    IF JSON_CONTAINS(additional_features, '"solar"') THEN
        SET features_cost = features_cost + 800000;
    END IF;

    SET min_cost = min_cost + features_cost;
    SET max_cost = max_cost + features_cost;
    SET avg_cost = (min_cost + max_cost) / 2;
END//

DELIMITER ;

-- =============================================
-- SAMPLE DATA FOR TESTING
-- =============================================

-- Insert sample customers
INSERT INTO users (email, password_hash, user_type, first_name, last_name, status, email_verified) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'customer', 'John', 'Doe', 'active', TRUE),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'customer', 'Jane', 'Smith', 'active', TRUE);

INSERT INTO customer_profiles (user_id, district, address) VALUES
(2, 'colombo', '123 Main Street, Colombo 03'),
(3, 'gampaha', '456 Gampaha Road, Gampaha');

-- Insert sample contractors
INSERT INTO users (email, password_hash, user_type, first_name, last_name, status, email_verified) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'contractor', 'Sunil', 'Perera', 'active', TRUE),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'contractor', 'Kamal', 'Silva', 'active', TRUE);

INSERT INTO contractor_profiles (user_id, business_name, years_experience, cida_registration_number, cida_grade, verification_status, average_rating, total_reviews) VALUES
(4, 'BuildPro Construction', 15, 'CIDA/C5/2020/001', 'C5', 'approved', 4.8, 124),
(5, 'Lanka Builders', 12, 'CIDA/C4/2021/002', 'C4', 'approved', 4.9, 89);

-- Link contractors to services
INSERT INTO contractor_services (contractor_id, service_category_id) VALUES
(1, 1), (1, 2), -- BuildPro: Residential & Commercial
(2, 1), (2, 3), (2, 6); -- Lanka Builders: Residential, Renovation, Interior

-- Link contractors to service areas
INSERT INTO contractor_service_areas (contractor_id, district_id) VALUES
(1, 1), (1, 2), -- BuildPro: Colombo, Gampaha
(2, 4), (2, 5); -- Lanka Builders: Kandy, Matale

-- Grant permissions (uncomment and modify as needed for your setup)
-- CREATE USER 'brick_click_user'@'localhost' IDENTIFIED BY 'your_secure_password_here';
-- GRANT ALL PRIVILEGES ON brick_click_db.* TO 'brick_click_user'@'localhost';
-- FLUSH PRIVILEGES;

-- =============================================
-- BACKUP AND MAINTENANCE RECOMMENDATIONS
-- =============================================

/*
RECOMMENDED MAINTENANCE TASKS:

1. Regular Backups:
   - Daily: mysqldump --single-transaction --routines --triggers brick_click_db > backup_$(date +%Y%m%d).sql
   - Weekly: Full database backup with binary logs

2. Index Optimization:
   - Monthly: ANALYZE TABLE on all tables
   - Quarterly: OPTIMIZE TABLE on heavily updated tables

3. Log Cleanup:
   - Weekly: Clean old activity_logs (older than 6 months)
   - Monthly: Archive old cost_estimates and notifications

4. Performance Monitoring:
   - Monitor slow query log
   - Check index usage with EXPLAIN
   - Monitor table sizes and growth

5. Security:
   - Regular password updates
   - Review user permissions
   - Monitor failed login attempts
   - Keep MySQL version updated

6. Data Integrity:
   - Monthly: Check foreign key constraints
   - Verify rating calculations
   - Validate JSON data integrity
*/