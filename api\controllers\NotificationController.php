<?php
/**
 * Notification Controller
 * Handles user notifications
 */

class NotificationController {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get user notifications
     */
    public function getNotifications($userId, $page = 1, $limit = 20, $unreadOnly = false) {
        $offset = ($page - 1) * $limit;
        
        $conditions = ["user_id = :user_id"];
        $params = [':user_id' => $userId];
        
        if ($unreadOnly) {
            $conditions[] = "is_read = FALSE";
        }
        
        $whereClause = "WHERE " . implode(" AND ", $conditions);
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM notifications $whereClause";
        $totalResult = $this->db->fetchOne($countSql, $params);
        $total = $totalResult['total'];
        
        // Get notifications
        $sql = "SELECT * FROM notifications $whereClause ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
        $params[':limit'] = $limit;
        $params[':offset'] = $offset;
        
        $notifications = $this->db->fetchAll($sql, $params);
        
        // Get unread count
        $unreadCountSql = "SELECT COUNT(*) as unread_count FROM notifications WHERE user_id = :user_id AND is_read = FALSE";
        $unreadResult = $this->db->fetchOne($unreadCountSql, [':user_id' => $userId]);
        $unreadCount = $unreadResult['unread_count'];
        
        // Calculate pagination
        $totalPages = ceil($total / $limit);
        
        return [
            'notifications' => $notifications,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_items' => $total,
                'items_per_page' => $limit,
                'has_next' => $page < $totalPages,
                'has_prev' => $page > 1
            ],
            'unread_count' => $unreadCount
        ];
    }
    
    /**
     * Mark notification as read
     */
    public function markAsRead($notificationId, $userId) {
        $sql = "UPDATE notifications SET is_read = TRUE, read_at = NOW() 
                WHERE id = :notification_id AND user_id = :user_id AND is_read = FALSE";
        
        $affected = $this->db->update($sql, [
            ':notification_id' => $notificationId,
            ':user_id' => $userId
        ]);
        
        if ($affected === 0) {
            throw new Exception("Notification not found or already read");
        }
        
        return true;
    }
    
    /**
     * Mark all notifications as read
     */
    public function markAllAsRead($userId) {
        $sql = "UPDATE notifications SET is_read = TRUE, read_at = NOW() 
                WHERE user_id = :user_id AND is_read = FALSE";
        
        $affected = $this->db->update($sql, [':user_id' => $userId]);
        
        return ['marked_count' => $affected];
    }
    
    /**
     * Create notification
     */
    public function createNotification($userId, $type, $title, $message, $relatedId = null, $relatedType = null, $actionUrl = null) {
        $sql = "INSERT INTO notifications (user_id, type, title, message, related_id, related_type, action_url) 
                VALUES (:user_id, :type, :title, :message, :related_id, :related_type, :action_url)";
        
        $notificationId = $this->db->insert($sql, [
            ':user_id' => $userId,
            ':type' => $type,
            ':title' => $title,
            ':message' => $message,
            ':related_id' => $relatedId,
            ':related_type' => $relatedType,
            ':action_url' => $actionUrl
        ]);
        
        return $notificationId;
    }
    
    /**
     * Create bulk notifications
     */
    public function createBulkNotifications($userIds, $type, $title, $message, $relatedId = null, $relatedType = null, $actionUrl = null) {
        if (empty($userIds) || !is_array($userIds)) {
            throw new Exception("User IDs array is required");
        }
        
        try {
            $this->db->beginTransaction();
            
            $sql = "INSERT INTO notifications (user_id, type, title, message, related_id, related_type, action_url) 
                    VALUES (:user_id, :type, :title, :message, :related_id, :related_type, :action_url)";
            
            $createdCount = 0;
            foreach ($userIds as $userId) {
                $this->db->insert($sql, [
                    ':user_id' => $userId,
                    ':type' => $type,
                    ':title' => $title,
                    ':message' => $message,
                    ':related_id' => $relatedId,
                    ':related_type' => $relatedType,
                    ':action_url' => $actionUrl
                ]);
                $createdCount++;
            }
            
            $this->db->commit();
            
            return ['created_count' => $createdCount];
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Delete notification
     */
    public function deleteNotification($notificationId, $userId) {
        $sql = "DELETE FROM notifications WHERE id = :notification_id AND user_id = :user_id";
        
        $affected = $this->db->delete($sql, [
            ':notification_id' => $notificationId,
            ':user_id' => $userId
        ]);
        
        if ($affected === 0) {
            throw new Exception("Notification not found");
        }
        
        return true;
    }
    
    /**
     * Get notification statistics
     */
    public function getStatistics($userId) {
        $sql = "SELECT 
                    COUNT(*) as total_notifications,
                    COUNT(CASE WHEN is_read = FALSE THEN 1 END) as unread_count,
                    COUNT(CASE WHEN is_read = TRUE THEN 1 END) as read_count,
                    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as recent_count
                FROM notifications 
                WHERE user_id = :user_id";
        
        $stats = $this->db->fetchOne($sql, [':user_id' => $userId]);
        
        // Get notifications by type
        $typeSql = "SELECT type, COUNT(*) as count 
                   FROM notifications 
                   WHERE user_id = :user_id 
                   GROUP BY type 
                   ORDER BY count DESC";
        
        $byType = $this->db->fetchAll($typeSql, [':user_id' => $userId]);
        
        return [
            'overall' => $stats,
            'by_type' => $byType
        ];
    }
    
    /**
     * Clean up old notifications
     */
    public function cleanupOldNotifications($days = 90) {
        $sql = "DELETE FROM notifications 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL :days DAY) 
                AND is_read = TRUE";
        
        $deleted = $this->db->delete($sql, [':days' => $days]);
        
        return ['deleted_count' => $deleted];
    }
    
    /**
     * Send system notification to all users
     */
    public function sendSystemNotification($title, $message, $userType = null, $actionUrl = null) {
        // Get user IDs based on type
        $sql = "SELECT id FROM users WHERE status = 'active'";
        $params = [];
        
        if ($userType) {
            $sql .= " AND user_type = :user_type";
            $params[':user_type'] = $userType;
        }
        
        $users = $this->db->fetchAll($sql, $params);
        $userIds = array_column($users, 'id');
        
        if (empty($userIds)) {
            throw new Exception("No users found");
        }
        
        return $this->createBulkNotifications($userIds, 'system', $title, $message, null, null, $actionUrl);
    }
    
    /**
     * Get recent notifications for dashboard
     */
    public function getRecentNotifications($userId, $limit = 5) {
        $sql = "SELECT * FROM notifications 
                WHERE user_id = :user_id 
                ORDER BY created_at DESC 
                LIMIT :limit";
        
        return $this->db->fetchAll($sql, [
            ':user_id' => $userId,
            ':limit' => $limit
        ]);
    }
    
    /**
     * Mark notification as clicked (for analytics)
     */
    public function markAsClicked($notificationId, $userId) {
        // First mark as read if not already
        $this->markAsRead($notificationId, $userId);
        
        // Log the click activity
        log_activity($userId, 'notification_clicked', 'notification', $notificationId, 'Notification clicked');
        
        return true;
    }
}

/**
 * Notification Helper Class
 * Static methods for common notification scenarios
 */
class NotificationHelper {
    
    /**
     * Send quote received notification
     */
    public static function quoteReceived($customerId, $contractorName, $quoteId) {
        $controller = new NotificationController();
        
        return $controller->createNotification(
            $customerId,
            'quote_received',
            'New Quote Received',
            "You received a new quote from {$contractorName}",
            $quoteId,
            'quote_response',
            "/my-quotes.html?quote={$quoteId}"
        );
    }
    
    /**
     * Send quote accepted notification
     */
    public static function quoteAccepted($contractorUserId, $customerName, $quoteId) {
        $controller = new NotificationController();
        
        return $controller->createNotification(
            $contractorUserId,
            'quote_accepted',
            'Quote Accepted!',
            "Your quote has been accepted by {$customerName}",
            $quoteId,
            'quote_response',
            "/contractor-dashboard.html"
        );
    }
    
    /**
     * Send project update notification
     */
    public static function projectUpdate($customerId, $projectName, $updateMessage, $projectId) {
        $controller = new NotificationController();
        
        return $controller->createNotification(
            $customerId,
            'project_update',
            'Project Update',
            "Update on {$projectName}: {$updateMessage}",
            $projectId,
            'project',
            "/project-details.html?id={$projectId}"
        );
    }
    
    /**
     * Send payment due notification
     */
    public static function paymentDue($customerId, $amount, $dueDate, $projectId) {
        $controller = new NotificationController();
        
        return $controller->createNotification(
            $customerId,
            'payment_due',
            'Payment Due',
            "Payment of LKR {$amount} is due on {$dueDate}",
            $projectId,
            'payment',
            "/payments.html?project={$projectId}"
        );
    }
    
    /**
     * Send review received notification
     */
    public static function reviewReceived($contractorUserId, $rating, $customerName, $reviewId) {
        $controller = new NotificationController();
        
        $stars = str_repeat('⭐', $rating);
        
        return $controller->createNotification(
            $contractorUserId,
            'review_received',
            'New Review Received',
            "You received a {$rating}-star review {$stars} from {$customerName}",
            $reviewId,
            'review',
            "/reviews.html?review={$reviewId}"
        );
    }
    
    /**
     * Send verification status notification
     */
    public static function verificationStatus($contractorUserId, $status, $reason = null) {
        $controller = new NotificationController();
        
        if ($status === 'approved') {
            $title = 'Account Verified!';
            $message = 'Congratulations! Your contractor account has been verified and approved.';
            $actionUrl = '/contractor-dashboard.html';
        } else {
            $title = 'Verification Update';
            $message = "Your verification status has been updated to: {$status}";
            if ($reason) {
                $message .= ". Reason: {$reason}";
            }
            $actionUrl = '/contractor-profile.html';
        }
        
        return $controller->createNotification(
            $contractorUserId,
            'verification_status',
            $title,
            $message,
            null,
            'contractor_profile',
            $actionUrl
        );
    }
}
?>
