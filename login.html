<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Login - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="construction, contractors, Sri Lanka, verified contractors, CIDA" name="keywords">
    <meta content="Login to Brick & Click - Sri Lanka's leading construction contractor platform" name="description">

    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border position-relative text-primary" style="width: 6rem; height: 6rem;" role="status"></div>
        <img class="position-absolute top-50 start-50 translate-middle" src="img/icons/icon-1.png" alt="Icon">
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5 wow fadeIn" data-wow-delay="0.1s">
        <a href="index.html" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="index.html" class="nav-item nav-link">Home</a>
                <a href="about.html" class="nav-item nav-link">About</a>
                <a href="service.html" class="nav-item nav-link">Services</a>
                <a href="contact.html" class="nav-item nav-link">Contact</a>
            </div>
            <div class="d-none d-lg-block">
                <a href="login.html" class="btn btn-outline-primary py-2 px-4 me-2 active">Login</a>
                <a href="register.html" class="btn btn-primary py-2 px-4">Sign Up</a>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Page Header Start -->
    <div class="container-fluid page-header py-5 mb-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <h1 class="display-1 text-white animated slideInDown">Login</h1>
            <nav aria-label="breadcrumb animated slideInDown">
                <ol class="breadcrumb text-uppercase mb-0">
                    <li class="breadcrumb-item"><a class="text-white" href="index.html">Home</a></li>
                    <li class="breadcrumb-item text-primary active" aria-current="page">Login</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Login Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6 col-md-8">
                    <div class="wow fadeInUp" data-wow-delay="0.1s">
                        <div class="bg-light rounded p-5">
                            <div class="text-center mb-4">
                                <h2 class="section-title text-center text-primary px-3">Welcome Back</h2>
                                <h1 class="mb-3">Login to Your Account</h1>
                                <p class="mb-4">Access your dashboard and manage your construction projects</p>
                            </div>

                            <form id="loginForm">
                                <div class="row g-3">
                                    <div class="col-12">
                                        <div class="form-floating">
                                            <input type="email" class="form-control" id="email" placeholder="Your Email" required>
                                            <label for="email">Email Address</label>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-floating">
                                            <input type="password" class="form-control" id="password" placeholder="Your Password" required>
                                            <label for="password">Password</label>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="rememberMe">
                                            <label class="form-check-label" for="rememberMe">
                                                Remember me
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <button class="btn btn-primary w-100 py-3" type="submit">Login</button>
                                    </div>
                                    <div class="col-12 text-center">
                                        <a href="forgot-password.html" class="text-primary">Forgot your password?</a>
                                    </div>
                                    <div class="col-12 text-center">
                                        <hr class="my-4">
                                        <p class="mb-0">Don't have an account? <a href="register.html" class="text-primary fw-bold">Sign up here</a></p>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Login End -->

    <!-- Language Selector Start -->
    <div class="container-xxl py-3">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6 col-md-8 text-center">
                    <div class="language-selector">
                        <label class="me-3">Language:</label>
                        <select class="form-select d-inline-block w-auto" id="languageSelect">
                            <option value="en" selected>English</option>
                            <option value="si">සිංහල</option>
                            <option value="ta">தமிழ்</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Language Selector End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-body footer mt-5 pt-5 px-0 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Contact Us</h3>
                    <p class="mb-2"><i class="fa fa-map-marker-alt text-primary me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt text-primary me-3"></i>+94 77 123 4567</p>
                    <p class="mb-2"><i class="fa fa-envelope text-primary me-3"></i><EMAIL></p>
                    <div class="d-flex pt-2">
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-twitter"></i></a>
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-facebook-f"></i></a>
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-youtube"></i></a>
                        <a class="btn btn-square btn-outline-body me-0" href=""><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Quick Links</h3>
                    <a class="btn btn-link" href="about.html">About Us</a>
                    <a class="btn btn-link" href="contact.html">Contact Us</a>
                    <a class="btn btn-link" href="service.html">Our Services</a>
                    <a class="btn btn-link" href="">Terms & Condition</a>
                    <a class="btn btn-link" href="">Support</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">For Contractors</h3>
                    <a class="btn btn-link" href="register.html">Join as Contractor</a>
                    <a class="btn btn-link" href="">Contractor Dashboard</a>
                    <a class="btn btn-link" href="">Success Stories</a>
                    <a class="btn btn-link" href="">Contractor Resources</a>
                    <a class="btn btn-link" href="">FAQ</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Newsletter</h3>
                    <p>Stay updated with the latest construction news and opportunities.</p>
                    <div class="position-relative mx-auto" style="max-width: 400px;">
                        <input class="form-control bg-transparent w-100 py-3 ps-4 pe-5" type="text" placeholder="Your email">
                        <button type="button" class="btn btn-primary py-2 position-absolute top-0 end-0 mt-2 me-2">SignUp</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid copyright">
            <div class="container">
                <div class="row">
                    <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                        &copy; <a href="#">Brick & Click</a>, All Right Reserved.
                    </div>
                    <div class="col-md-6 text-center text-md-end">
                        Designed By <a href="https://htmlcodex.com">HTML Codex</a>
                        <br> Distributed By: <a class="border-bottom" href="https://themewagon.com" target="_blank">ThemeWagon</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>

    <!-- Template Javascript -->
    <script src="js/main.js"></script>

    <!-- Login Page Javascript -->
    <script>
        // API Configuration
        const API_BASE_URL = 'api';

        // Show loading state
        function showLoading(show = true) {
            const submitBtn = document.querySelector('button[type="submit"]');

            if (show) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Signing In...';
            } else {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fa fa-sign-in-alt me-2"></i>Sign In';
            }
        }

        // Show error message
        function showError(message) {
            // Create or update error alert
            let errorDiv = document.getElementById('errorAlert');
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.id = 'errorAlert';
                errorDiv.className = 'alert alert-danger alert-dismissible fade show mt-3';
                errorDiv.innerHTML = `
                    <span id="errorMessage"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.querySelector('.login-form').insertBefore(errorDiv, document.getElementById('loginForm'));
            }

            document.getElementById('errorMessage').textContent = message;
            errorDiv.style.display = 'block';

            // Auto-hide after 5 seconds
            setTimeout(() => {
                if (errorDiv) {
                    errorDiv.style.display = 'none';
                }
            }, 5000);
        }

        // Show success message
        function showSuccess(message) {
            let successDiv = document.getElementById('successAlert');
            if (!successDiv) {
                successDiv = document.createElement('div');
                successDiv.id = 'successAlert';
                successDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
                successDiv.innerHTML = `
                    <span id="successMessage"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.querySelector('.login-form').insertBefore(successDiv, document.getElementById('loginForm'));
            }

            document.getElementById('successMessage').textContent = message;
            successDiv.style.display = 'block';
        }

        // API call helper
        async function apiCall(endpoint, method = 'GET', data = null) {
            const config = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include'
            };

            if (data) {
                config.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(`${API_BASE_URL}/${endpoint}`, config);
                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.error || 'An error occurred');
                }

                return result;
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        }

        // Handle login form submission
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            const userType = document.getElementById('userType').value;

            // Basic validation
            if (!email || !password || !userType) {
                showError('Please fill in all fields');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showError('Please enter a valid email address');
                return;
            }

            showLoading(true);

            try {
                // Call login API
                const response = await apiCall('auth/login', 'POST', {
                    email: email,
                    password: password
                });

                if (response.success) {
                    const user = response.data;

                    // Verify user type matches selection
                    if (user.user_type !== userType) {
                        showError(`This account is registered as ${user.user_type}, not ${userType}`);
                        showLoading(false);
                        return;
                    }

                    // Store user data
                    localStorage.setItem('user', JSON.stringify(user));
                    localStorage.setItem('loginTime', Date.now().toString());

                    // Remember email if checkbox is checked
                    const rememberMe = document.getElementById('rememberMe').checked;
                    if (rememberMe) {
                        localStorage.setItem('rememberedEmail', email);
                        localStorage.setItem('rememberMe', 'true');
                    } else {
                        localStorage.removeItem('rememberedEmail');
                        localStorage.removeItem('rememberMe');
                    }

                    showSuccess('Login successful! Redirecting...');

                    // Redirect based on user type
                    setTimeout(() => {
                        switch (userType) {
                            case 'customer':
                                window.location.href = 'customer-dashboard.html';
                                break;
                            case 'contractor':
                                window.location.href = 'contractor-dashboard.html';
                                break;
                            case 'admin':
                                window.location.href = 'admin-dashboard.html';
                                break;
                            default:
                                window.location.href = 'index.html';
                        }
                    }, 1000);
                } else {
                    showError(response.error || 'Login failed');
                }
            } catch (error) {
                showError(error.message || 'Login failed. Please try again.');
            } finally {
                showLoading(false);
            }
        });

        // Handle forgot password
        document.addEventListener('click', async function(e) {
            if (e.target.id === 'forgotPasswordLink') {
                e.preventDefault();

                const email = document.getElementById('email').value.trim();

                if (!email) {
                    showError('Please enter your email address first');
                    return;
                }

                try {
                    const response = await apiCall('auth/forgot-password', 'POST', {
                        email: email
                    });

                    if (response.success) {
                        showSuccess('Password reset instructions have been sent to your email');
                    } else {
                        showError(response.error || 'Failed to send reset email');
                    }
                } catch (error) {
                    showError(error.message || 'Failed to send reset email');
                }
            }
        });

        // Language selector
        document.getElementById('languageSelect').addEventListener('change', function(e) {
            const selectedLang = e.target.value;
            localStorage.setItem('preferredLanguage', selectedLang);
            console.log('Language changed to:', selectedLang);
            // TODO: Implement language switching
        });

        // Check if user is already logged in
        document.addEventListener('DOMContentLoaded', function() {
            const user = localStorage.getItem('user');
            const loginTime = localStorage.getItem('loginTime');

            if (user && loginTime) {
                const userData = JSON.parse(user);
                const timeDiff = Date.now() - parseInt(loginTime);
                const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds

                // If logged in within the last hour, redirect to dashboard
                if (timeDiff < oneHour) {
                    switch (userData.user_type) {
                        case 'customer':
                            window.location.href = 'customer-dashboard.html';
                            break;
                        case 'contractor':
                            window.location.href = 'contractor-dashboard.html';
                            break;
                        case 'admin':
                            window.location.href = 'admin-dashboard.html';
                            break;
                    }
                }
            }

            // Load preferred language
            const preferredLang = localStorage.getItem('preferredLanguage');
            if (preferredLang) {
                document.getElementById('languageSelect').value = preferredLang;
            }

            // Load remembered email
            const rememberMe = localStorage.getItem('rememberMe');
            const rememberedEmail = localStorage.getItem('rememberedEmail');

            if (rememberMe === 'true' && rememberedEmail) {
                document.getElementById('email').value = rememberedEmail;
                document.getElementById('rememberMe').checked = true;
            }
        });
    </script>
</body>

</html>
