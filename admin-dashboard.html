<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Admin Dashboard - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="admin dashboard, construction, Sri Lanka" name="keywords">
    <meta content="Admin Dashboard - Brick & Click" name="description">

    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border position-relative text-primary" style="width: 6rem; height: 6rem;" role="status"></div>
        <img class="position-absolute top-50 start-50 translate-middle" src="img/icons/icon-1.png" alt="Icon">
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5 wow fadeIn" data-wow-delay="0.1s">
        <a href="index.html" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="admin-dashboard.html" class="nav-item nav-link active">Dashboard</a>
                <a href="admin-contractors.html" class="nav-item nav-link">Contractors</a>
                <a href="admin-customers.html" class="nav-item nav-link">Customers</a>
                <a href="admin-projects.html" class="nav-item nav-link">Projects</a>
                <a href="admin-reviews.html" class="nav-item nav-link">Reviews</a>
                <a href="admin-reports.html" class="nav-item nav-link">Reports</a>
            </div>
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                    <i class="fa fa-user-shield me-2"></i><span id="adminName">Admin</span>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="admin-profile.html"><i class="fa fa-user me-2"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="admin-settings.html"><i class="fa fa-cog me-2"></i>Settings</a></li>
                    <li><a class="dropdown-item" href="admin-logs.html"><i class="fa fa-list me-2"></i>Activity Logs</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Page Header Start -->
    <div class="container-fluid page-header py-5 mb-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <h1 class="display-1 text-white animated slideInDown">Admin Dashboard</h1>
            <nav aria-label="breadcrumb animated slideInDown">
                <ol class="breadcrumb text-uppercase mb-0">
                    <li class="breadcrumb-item"><a class="text-white" href="index.html">Home</a></li>
                    <li class="breadcrumb-item text-primary active" aria-current="page">Admin Dashboard</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Dashboard Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <!-- Welcome Section -->
            <div class="row mb-5">
                <div class="col-12">
                    <div class="bg-light rounded p-4">
                        <div class="row align-items-center">
                            <div class="col-lg-8">
                                <h3 class="text-primary mb-2">Welcome to Admin Dashboard</h3>
                                <p class="mb-0">Manage the Brick & Click platform, verify contractors, and monitor system activities.</p>
                            </div>
                            <div class="col-lg-4 text-end">
                                <div class="system-status">
                                    <span class="badge bg-success"><i class="fa fa-check-circle me-1"></i>System Online</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row g-4 mb-5">
                <div class="col-lg-3 col-md-6">
                    <div class="bg-white rounded shadow-sm p-4 text-center">
                        <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fa fa-users text-white"></i>
                        </div>
                        <h4 class="text-primary mb-1" id="totalCustomers">0</h4>
                        <p class="mb-0">Total Customers</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="bg-white rounded shadow-sm p-4 text-center">
                        <div class="bg-success rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fa fa-hard-hat text-white"></i>
                        </div>
                        <h4 class="text-success mb-1" id="verifiedContractors">0</h4>
                        <p class="mb-0">Verified Contractors</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="bg-white rounded shadow-sm p-4 text-center">
                        <div class="bg-warning rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fa fa-clock text-white"></i>
                        </div>
                        <h4 class="text-warning mb-1" id="pendingVerifications">0</h4>
                        <p class="mb-0">Pending Verifications</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="bg-white rounded shadow-sm p-4 text-center">
                        <div class="bg-info rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fa fa-tools text-white"></i>
                        </div>
                        <h4 class="text-info mb-1" id="activeProjects">0</h4>
                        <p class="mb-0">Active Projects</p>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="row g-4">
                <!-- Pending Verifications -->
                <div class="col-lg-8">
                    <div class="bg-white rounded shadow-sm p-4 mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="text-primary mb-0">
                                <i class="fa fa-user-check me-2"></i>Pending Contractor Verifications
                            </h5>
                            <a href="admin-contractors.html" class="btn btn-outline-primary btn-sm">View All</a>
                        </div>
                        <div id="pendingVerificationsList">
                            <div class="text-center py-4">
                                <i class="fa fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="text-muted mt-2">Loading pending verifications...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activities -->
                    <div class="bg-white rounded shadow-sm p-4">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="text-primary mb-0">
                                <i class="fa fa-list me-2"></i>Recent System Activities
                            </h5>
                            <a href="admin-logs.html" class="btn btn-outline-primary btn-sm">View All</a>
                        </div>
                        <div id="recentActivities">
                            <div class="text-center py-4">
                                <i class="fa fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="text-muted mt-2">Loading recent activities...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Quick Actions -->
                    <div class="bg-white rounded shadow-sm p-4 mb-4">
                        <h5 class="text-primary mb-4">
                            <i class="fa fa-bolt me-2"></i>Quick Actions
                        </h5>
                        <div class="d-grid gap-2">
                            <a href="admin-contractors.html" class="btn btn-outline-primary">
                                <i class="fa fa-user-check me-2"></i>Verify Contractors
                            </a>
                            <a href="admin-reviews.html" class="btn btn-outline-primary">
                                <i class="fa fa-star me-2"></i>Moderate Reviews
                            </a>
                            <a href="admin-reports.html" class="btn btn-outline-primary">
                                <i class="fa fa-flag me-2"></i>Handle Reports
                            </a>
                            <a href="admin-settings.html" class="btn btn-outline-primary">
                                <i class="fa fa-cog me-2"></i>System Settings
                            </a>
                        </div>
                    </div>

                    <!-- System Statistics -->
                    <div class="bg-white rounded shadow-sm p-4 mb-4">
                        <h5 class="text-primary mb-4">
                            <i class="fa fa-chart-bar me-2"></i>System Statistics
                        </h5>
                        <div class="system-stats">
                            <div class="d-flex justify-content-between mb-3">
                                <span>Total Users</span>
                                <strong id="totalUsers">0</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>Total Projects</span>
                                <strong id="totalProjects">0</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>Total Reviews</span>
                                <strong id="totalReviews">0</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>Platform Revenue</span>
                                <strong id="platformRevenue">LKR 0</strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Success Rate</span>
                                <strong id="successRate">0%</strong>
                            </div>
                        </div>
                    </div>

                    <!-- Alerts -->
                    <div class="bg-white rounded shadow-sm p-4">
                        <h5 class="text-primary mb-4">
                            <i class="fa fa-exclamation-triangle me-2"></i>System Alerts
                        </h5>
                        <div id="systemAlerts">
                            <div class="alert alert-warning alert-sm">
                                <i class="fa fa-clock me-2"></i>
                                <strong>5</strong> contractors pending verification
                            </div>
                            <div class="alert alert-info alert-sm">
                                <i class="fa fa-star me-2"></i>
                                <strong>3</strong> reviews pending moderation
                            </div>
                            <div class="alert alert-danger alert-sm">
                                <i class="fa fa-flag me-2"></i>
                                <strong>2</strong> reports need attention
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Dashboard End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-body footer mt-5 pt-5 px-0 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Contact Us</h3>
                    <p class="mb-2"><i class="fa fa-map-marker-alt text-primary me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt text-primary me-3"></i>+94 77 123 4567</p>
                    <p class="mb-2"><i class="fa fa-envelope text-primary me-3"></i><EMAIL></p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Admin Links</h3>
                    <a class="btn btn-link" href="admin-contractors.html">Manage Contractors</a>
                    <a class="btn btn-link" href="admin-customers.html">Manage Customers</a>
                    <a class="btn btn-link" href="admin-projects.html">Monitor Projects</a>
                    <a class="btn btn-link" href="admin-reviews.html">Review Moderation</a>
                    <a class="btn btn-link" href="admin-reports.html">Handle Reports</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">System</h3>
                    <a class="btn btn-link" href="admin-settings.html">System Settings</a>
                    <a class="btn btn-link" href="admin-logs.html">Activity Logs</a>
                    <a class="btn btn-link" href="admin-backup.html">Backup & Restore</a>
                    <a class="btn btn-link" href="admin-analytics.html">Analytics</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Platform Status</h3>
                    <p>System uptime: <strong>99.9%</strong></p>
                    <p>Active users: <strong id="activeUsersCount">0</strong></p>
                    <p>Last backup: <strong id="lastBackupTime">Loading...</strong></p>
                </div>
            </div>
        </div>
        <div class="container-fluid copyright">
            <div class="container">
                <div class="row">
                    <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                        &copy; <a href="#">Brick & Click</a> Admin Panel, All Right Reserved.
                    </div>
                    <div class="col-md-6 text-center text-md-end">
                        Admin Dashboard v1.0
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>

    <!-- Template Javascript -->
    <script src="js/main.js"></script>

    <!-- Admin Dashboard Javascript -->
    <script>
        // API Configuration
        const API_BASE_URL = 'api';
        let currentUser = null;

        // API call helper
        async function apiCall(endpoint, method = 'GET', data = null) {
            const config = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include'
            };

            if (data) {
                config.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(`${API_BASE_URL}/${endpoint}`, config);
                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.error || 'An error occurred');
                }

                return result;
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        }

        // Check authentication
        function checkAuth() {
            const user = localStorage.getItem('user');
            const loginTime = localStorage.getItem('loginTime');

            if (!user || !loginTime) {
                window.location.href = 'login.html';
                return false;
            }

            const userData = JSON.parse(user);
            const timeDiff = Date.now() - parseInt(loginTime);
            const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds

            if (timeDiff > oneHour) {
                localStorage.removeItem('user');
                localStorage.removeItem('loginTime');
                window.location.href = 'login.html';
                return false;
            }

            if (userData.user_type !== 'admin') {
                window.location.href = 'login.html';
                return false;
            }

            currentUser = userData;
            return true;
        }

        // Logout function
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                localStorage.removeItem('user');
                localStorage.removeItem('loginTime');

                // Call logout API
                apiCall('auth/logout', 'POST').catch(console.error);

                window.location.href = 'login.html';
            }
        }

        // Load dashboard data
        async function loadDashboardData() {
            try {
                // Update admin name
                if (currentUser) {
                    document.getElementById('adminName').textContent = currentUser.first_name + ' ' + currentUser.last_name;
                }

                // Load statistics (placeholder - implement actual API calls)
                loadStatistics();

                // Load pending verifications
                loadPendingVerifications();

                // Load recent activities
                loadRecentActivities();

                // Load system status
                loadSystemStatus();

            } catch (error) {
                console.error('Error loading dashboard data:', error);
                showError('Failed to load dashboard data');
            }
        }

        // Load statistics
        function loadStatistics() {
            // Placeholder data - implement actual API calls
            document.getElementById('totalCustomers').textContent = '1,247';
            document.getElementById('verifiedContractors').textContent = '89';
            document.getElementById('pendingVerifications').textContent = '5';
            document.getElementById('activeProjects').textContent = '156';

            // System statistics
            document.getElementById('totalUsers').textContent = '1,336';
            document.getElementById('totalProjects').textContent = '423';
            document.getElementById('totalReviews').textContent = '892';
            document.getElementById('platformRevenue').textContent = 'LKR 12,450,000';
            document.getElementById('successRate').textContent = '94.2%';

            // Footer stats
            document.getElementById('activeUsersCount').textContent = '234';
            document.getElementById('lastBackupTime').textContent = '2 hours ago';
        }

        // Load pending verifications
        function loadPendingVerifications() {
            const container = document.getElementById('pendingVerificationsList');

            // Placeholder data
            const pendingVerifications = [
                {
                    id: 1,
                    businessName: 'ABC Construction',
                    contractorName: 'John Silva',
                    cidaGrade: 'C5',
                    submittedDate: '2025-01-18',
                    experience: '15 years'
                },
                {
                    id: 2,
                    businessName: 'Lanka Builders',
                    contractorName: 'Kamal Perera',
                    cidaGrade: 'C4',
                    submittedDate: '2025-01-17',
                    experience: '12 years'
                }
            ];

            if (pendingVerifications.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fa fa-check-circle fa-3x text-success mb-3"></i>
                        <p class="text-muted">No pending verifications</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = pendingVerifications.map(verification => `
                <div class="border-bottom pb-3 mb-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">${verification.businessName}</h6>
                            <p class="text-muted mb-1">
                                <i class="fa fa-user me-1"></i>${verification.contractorName} •
                                <i class="fa fa-certificate me-1"></i>CIDA Grade: ${verification.cidaGrade}
                            </p>
                            <p class="text-muted mb-0">
                                <i class="fa fa-clock me-1"></i>Experience: ${verification.experience} •
                                <i class="fa fa-calendar me-1"></i>Submitted: ${verification.submittedDate}
                            </p>
                        </div>
                        <div class="text-end">
                            <button class="btn btn-success btn-sm me-1" onclick="approveContractor(${verification.id})">
                                <i class="fa fa-check"></i> Approve
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="rejectContractor(${verification.id})">
                                <i class="fa fa-times"></i> Reject
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Load recent activities
        function loadRecentActivities() {
            const container = document.getElementById('recentActivities');

            // Placeholder data
            const activities = [
                {
                    action: 'User Registration',
                    description: 'New customer registered: <EMAIL>',
                    timestamp: '2025-01-19 10:30 AM',
                    type: 'info'
                },
                {
                    action: 'Contractor Verification',
                    description: 'Approved contractor: ABC Construction',
                    timestamp: '2025-01-19 09:15 AM',
                    type: 'success'
                },
                {
                    action: 'Review Moderation',
                    description: 'Moderated review for Project #123',
                    timestamp: '2025-01-19 08:45 AM',
                    type: 'warning'
                },
                {
                    action: 'System Backup',
                    description: 'Automated system backup completed',
                    timestamp: '2025-01-19 02:00 AM',
                    type: 'info'
                }
            ];

            container.innerHTML = activities.map(activity => {
                const iconClass = activity.type === 'success' ? 'fa-check-circle text-success' :
                                activity.type === 'warning' ? 'fa-exclamation-triangle text-warning' :
                                activity.type === 'danger' ? 'fa-times-circle text-danger' :
                                'fa-info-circle text-info';

                return `
                    <div class="border-bottom pb-3 mb-3">
                        <div class="d-flex align-items-start">
                            <i class="fa ${iconClass} me-3 mt-1"></i>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">${activity.action}</h6>
                                <p class="text-muted mb-1">${activity.description}</p>
                                <small class="text-muted">${activity.timestamp}</small>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Load system status
        function loadSystemStatus() {
            // Placeholder implementation
            console.log('System status loaded');
        }

        // Approve contractor
        async function approveContractor(contractorId) {
            if (confirm('Are you sure you want to approve this contractor?')) {
                try {
                    // Placeholder API call
                    console.log('Approving contractor:', contractorId);
                    showSuccess('Contractor approved successfully');
                    loadPendingVerifications(); // Refresh the list
                } catch (error) {
                    showError('Failed to approve contractor');
                }
            }
        }

        // Reject contractor
        async function rejectContractor(contractorId) {
            const reason = prompt('Please provide a reason for rejection:');
            if (reason) {
                try {
                    // Placeholder API call
                    console.log('Rejecting contractor:', contractorId, 'Reason:', reason);
                    showSuccess('Contractor rejected successfully');
                    loadPendingVerifications(); // Refresh the list
                } catch (error) {
                    showError('Failed to reject contractor');
                }
            }
        }

        // Show success message
        function showSuccess(message) {
            // Simple success display - you can enhance this
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        // Show error message
        function showError(message) {
            // Simple error display - you can enhance this
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadDashboardData();

                // Refresh data every 5 minutes
                setInterval(loadDashboardData, 5 * 60 * 1000);
            }
        });
    </script>
</body>

</html>
