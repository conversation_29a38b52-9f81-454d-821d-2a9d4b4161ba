<?php
/**
 * Contractor Controller
 * Handles contractor-related operations
 */

class ContractorController {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Search contractors with filters
     */
    public function search($filters = [], $page = 1, $limit = 12) {
        $offset = ($page - 1) * $limit;
        
        // Base query using the contractor search view
        $sql = "SELECT * FROM contractor_search_view WHERE verification_status = 'approved'";
        $params = [];
        $conditions = [];
        
        // Apply filters
        if (!empty($filters['search'])) {
            $conditions[] = "(business_name LIKE :search OR CONCAT(first_name, ' ', last_name) LIKE :search)";
            $params[':search'] = '%' . $filters['search'] . '%';
        }
        
        if (!empty($filters['district'])) {
            $conditions[] = "contractor_id IN (
                SELECT csa.contractor_id 
                FROM contractor_service_areas csa 
                JOIN districts d ON csa.district_id = d.id 
                WHERE d.name = :district
            )";
            $params[':district'] = $filters['district'];
        }
        
        if (!empty($filters['service_category'])) {
            $conditions[] = "contractor_id IN (
                SELECT cs.contractor_id 
                FROM contractor_services cs 
                JOIN service_categories sc ON cs.service_category_id = sc.id 
                WHERE sc.name = :service_category
            )";
            $params[':service_category'] = $filters['service_category'];
        }
        
        if (!empty($filters['min_rating']) && is_numeric($filters['min_rating'])) {
            $conditions[] = "average_rating >= :min_rating";
            $params[':min_rating'] = floatval($filters['min_rating']);
        }
        
        if (!empty($filters['cida_grade'])) {
            $conditions[] = "cida_grade = :cida_grade";
            $params[':cida_grade'] = $filters['cida_grade'];
        }
        
        if (!empty($filters['min_experience']) && is_numeric($filters['min_experience'])) {
            $conditions[] = "years_experience >= :min_experience";
            $params[':min_experience'] = intval($filters['min_experience']);
        }
        
        // Add conditions to query
        if (!empty($conditions)) {
            $sql .= " AND " . implode(" AND ", $conditions);
        }
        
        // Get total count for pagination
        $countSql = "SELECT COUNT(*) as total FROM (" . $sql . ") as filtered_results";
        $totalResult = $this->db->fetchOne($countSql, $params);
        $total = $totalResult['total'];
        
        // Add sorting
        $sortBy = $filters['sort_by'] ?? 'rating';
        switch ($sortBy) {
            case 'rating':
                $sql .= " ORDER BY average_rating DESC, total_reviews DESC";
                break;
            case 'reviews':
                $sql .= " ORDER BY total_reviews DESC, average_rating DESC";
                break;
            case 'experience':
                $sql .= " ORDER BY years_experience DESC, average_rating DESC";
                break;
            case 'name':
                $sql .= " ORDER BY business_name ASC";
                break;
            default:
                $sql .= " ORDER BY average_rating DESC, total_reviews DESC";
        }
        
        // Add pagination
        $sql .= " LIMIT :limit OFFSET :offset";
        $params[':limit'] = $limit;
        $params[':offset'] = $offset;
        
        $contractors = $this->db->fetchAll($sql, $params);
        
        // Calculate pagination info
        $totalPages = ceil($total / $limit);
        $hasNext = $page < $totalPages;
        $hasPrev = $page > 1;
        
        return [
            'contractors' => $contractors,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_items' => $total,
                'items_per_page' => $limit,
                'has_next' => $hasNext,
                'has_prev' => $hasPrev
            ],
            'filters_applied' => array_filter($filters)
        ];
    }
    
    /**
     * Get contractor details
     */
    public function getDetails($contractorId) {
        // Get basic contractor info
        $sql = "SELECT cp.*, u.first_name, u.last_name, u.email, u.phone, u.profile_image,
                       GROUP_CONCAT(DISTINCT sc.name) as services,
                       GROUP_CONCAT(DISTINCT d.name) as service_areas
                FROM contractor_profiles cp
                JOIN users u ON cp.user_id = u.id
                LEFT JOIN contractor_services cs ON cp.id = cs.contractor_id
                LEFT JOIN service_categories sc ON cs.service_category_id = sc.id
                LEFT JOIN contractor_service_areas csa ON cp.id = csa.contractor_id
                LEFT JOIN districts d ON csa.district_id = d.id
                WHERE cp.id = :contractor_id AND cp.verification_status = 'approved'
                GROUP BY cp.id";
        
        $contractor = $this->db->fetchOne($sql, [':contractor_id' => $contractorId]);
        
        if (!$contractor) {
            throw new Exception("Contractor not found");
        }
        
        // Get portfolio
        $portfolioSql = "SELECT cp.*, GROUP_CONCAT(pi.image_path) as images
                        FROM contractor_portfolios cp
                        LEFT JOIN portfolio_images pi ON cp.id = pi.portfolio_id
                        WHERE cp.contractor_id = :contractor_id AND cp.status = 'active'
                        GROUP BY cp.id
                        ORDER BY cp.is_featured DESC, cp.display_order ASC, cp.completion_date DESC";
        
        $contractor['portfolio'] = $this->db->fetchAll($portfolioSql, [':contractor_id' => $contractorId]);
        
        // Get recent reviews
        $reviewsSql = "SELECT r.*, CONCAT(u.first_name, ' ', u.last_name) as customer_name,
                              rr.response_text as contractor_response
                       FROM reviews r
                       JOIN users u ON r.customer_id = u.id
                       LEFT JOIN review_responses rr ON r.id = rr.review_id
                       WHERE r.contractor_id = :contractor_id AND r.status = 'approved'
                       ORDER BY r.created_at DESC
                       LIMIT 10";
        
        $contractor['recent_reviews'] = $this->db->fetchAll($reviewsSql, [':contractor_id' => $contractorId]);
        
        // Convert comma-separated strings to arrays
        $contractor['services'] = $contractor['services'] ? explode(',', $contractor['services']) : [];
        $contractor['service_areas'] = $contractor['service_areas'] ? explode(',', $contractor['service_areas']) : [];
        
        return $contractor;
    }
    
    /**
     * Get contractor profile (for authenticated contractor)
     */
    public function getProfile($userId) {
        $sql = "SELECT cp.*, u.first_name, u.last_name, u.email, u.phone, u.profile_image,
                       GROUP_CONCAT(DISTINCT sc.id, ':', sc.name) as services,
                       GROUP_CONCAT(DISTINCT d.id, ':', d.name) as service_areas
                FROM contractor_profiles cp
                JOIN users u ON cp.user_id = u.id
                LEFT JOIN contractor_services cs ON cp.id = cs.contractor_id
                LEFT JOIN service_categories sc ON cs.service_category_id = sc.id
                LEFT JOIN contractor_service_areas csa ON cp.id = csa.contractor_id
                LEFT JOIN districts d ON csa.district_id = d.id
                WHERE cp.user_id = :user_id
                GROUP BY cp.id";
        
        $profile = $this->db->fetchOne($sql, [':user_id' => $userId]);
        
        if (!$profile) {
            throw new Exception("Contractor profile not found");
        }
        
        // Parse services and service areas
        $profile['services'] = $this->parseIdNamePairs($profile['services']);
        $profile['service_areas'] = $this->parseIdNamePairs($profile['service_areas']);
        
        // Get statistics
        $statsSql = "SELECT 
                        COUNT(DISTINCT p.id) as total_projects,
                        COUNT(DISTINCT CASE WHEN p.status IN ('planning', 'in_progress') THEN p.id END) as active_projects,
                        COUNT(DISTINCT cf.id) as total_favorites,
                        AVG(CASE WHEN r.created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH) THEN r.rating END) as recent_rating
                     FROM contractor_profiles cp
                     LEFT JOIN projects p ON cp.id = p.contractor_id
                     LEFT JOIN customer_favorites cf ON cp.id = cf.contractor_id
                     LEFT JOIN reviews r ON cp.id = r.contractor_id AND r.status = 'approved'
                     WHERE cp.user_id = :user_id";
        
        $stats = $this->db->fetchOne($statsSql, [':user_id' => $userId]);
        $profile['statistics'] = $stats;
        
        return $profile;
    }
    
    /**
     * Update contractor profile
     */
    public function updateProfile($userId, $data) {
        // Get contractor ID
        $contractor = $this->db->fetchOne(
            "SELECT id FROM contractor_profiles WHERE user_id = :user_id",
            [':user_id' => $userId]
        );
        
        if (!contractor) {
            throw new Exception("Contractor profile not found");
        }
        
        $contractorId = $contractor['id'];
        
        try {
            $this->db->beginTransaction();
            
            // Update user table
            if (isset($data['first_name']) || isset($data['last_name']) || isset($data['phone'])) {
                $userFields = [];
                $userParams = [':user_id' => $userId];
                
                if (isset($data['first_name'])) {
                    $userFields[] = "first_name = :first_name";
                    $userParams[':first_name'] = $data['first_name'];
                }
                
                if (isset($data['last_name'])) {
                    $userFields[] = "last_name = :last_name";
                    $userParams[':last_name'] = $data['last_name'];
                }
                
                if (isset($data['phone'])) {
                    $userFields[] = "phone = :phone";
                    $userParams[':phone'] = $data['phone'];
                }
                
                if (!empty($userFields)) {
                    $userSql = "UPDATE users SET " . implode(', ', $userFields) . " WHERE id = :user_id";
                    $this->db->update($userSql, $userParams);
                }
            }
            
            // Update contractor profile
            $profileFields = [];
            $profileParams = [':contractor_id' => $contractorId];
            
            $allowedFields = ['business_name', 'business_address', 'description', 'website_url'];
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $profileFields[] = "$field = :$field";
                    $profileParams[":$field"] = $data[$field];
                }
            }
            
            if (!empty($profileFields)) {
                $profileSql = "UPDATE contractor_profiles SET " . implode(', ', $profileFields) . " WHERE id = :contractor_id";
                $this->db->update($profileSql, $profileParams);
            }
            
            // Update services
            if (isset($data['services']) && is_array($data['services'])) {
                // Remove existing services
                $this->db->delete(
                    "DELETE FROM contractor_services WHERE contractor_id = :contractor_id",
                    [':contractor_id' => $contractorId]
                );
                
                // Add new services
                foreach ($data['services'] as $serviceId) {
                    $this->db->insert(
                        "INSERT INTO contractor_services (contractor_id, service_category_id) VALUES (:contractor_id, :service_id)",
                        [':contractor_id' => $contractorId, ':service_id' => $serviceId]
                    );
                }
            }
            
            // Update service areas
            if (isset($data['service_areas']) && is_array($data['service_areas'])) {
                // Remove existing service areas
                $this->db->delete(
                    "DELETE FROM contractor_service_areas WHERE contractor_id = :contractor_id",
                    [':contractor_id' => $contractorId]
                );
                
                // Add new service areas
                foreach ($data['service_areas'] as $districtId) {
                    $this->db->insert(
                        "INSERT INTO contractor_service_areas (contractor_id, district_id) VALUES (:contractor_id, :district_id)",
                        [':contractor_id' => $contractorId, ':district_id' => $districtId]
                    );
                }
            }
            
            $this->db->commit();
            
            // Log activity
            log_activity($userId, 'profile_updated', 'contractor_profile', $contractorId, 'Contractor profile updated');
            
            return ['contractor_id' => $contractorId];
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Get contractor portfolio
     */
    public function getPortfolio($userId) {
        $contractorId = $this->getContractorId($userId);
        
        $sql = "SELECT cp.*, GROUP_CONCAT(pi.image_path ORDER BY pi.display_order) as images
                FROM contractor_portfolios cp
                LEFT JOIN portfolio_images pi ON cp.id = pi.portfolio_id
                WHERE cp.contractor_id = :contractor_id
                GROUP BY cp.id
                ORDER BY cp.is_featured DESC, cp.display_order ASC, cp.completion_date DESC";
        
        $portfolio = $this->db->fetchAll($sql, [':contractor_id' => $contractorId]);
        
        // Convert image strings to arrays
        foreach ($portfolio as &$item) {
            $item['images'] = $item['images'] ? explode(',', $item['images']) : [];
        }
        
        return $portfolio;
    }
    
    /**
     * Add portfolio item
     */
    public function addPortfolioItem($userId, $data) {
        $contractorId = $this->getContractorId($userId);
        
        validate_required_fields($data, ['project_name', 'project_description']);
        
        $sql = "INSERT INTO contractor_portfolios (contractor_id, project_name, project_description, project_type, completion_date, project_value, client_name, location, duration_weeks, featured_image) 
                VALUES (:contractor_id, :project_name, :project_description, :project_type, :completion_date, :project_value, :client_name, :location, :duration_weeks, :featured_image)";
        
        $portfolioId = $this->db->insert($sql, [
            ':contractor_id' => $contractorId,
            ':project_name' => $data['project_name'],
            ':project_description' => $data['project_description'],
            ':project_type' => $data['project_type'] ?? null,
            ':completion_date' => $data['completion_date'] ?? null,
            ':project_value' => $data['project_value'] ?? null,
            ':client_name' => $data['client_name'] ?? null,
            ':location' => $data['location'] ?? null,
            ':duration_weeks' => $data['duration_weeks'] ?? null,
            ':featured_image' => $data['featured_image'] ?? null
        ]);
        
        // Log activity
        log_activity($userId, 'portfolio_item_added', 'contractor_portfolio', $portfolioId, 'Portfolio item added');
        
        return ['portfolio_id' => $portfolioId];
    }
    
    /**
     * Helper method to get contractor ID from user ID
     */
    private function getContractorId($userId) {
        $contractor = $this->db->fetchOne(
            "SELECT id FROM contractor_profiles WHERE user_id = :user_id",
            [':user_id' => $userId]
        );
        
        if (!$contractor) {
            throw new Exception("Contractor profile not found");
        }
        
        return $contractor['id'];
    }
    
    /**
     * Helper method to parse ID:Name pairs
     */
    private function parseIdNamePairs($string) {
        if (!$string) return [];
        
        $pairs = explode(',', $string);
        $result = [];
        
        foreach ($pairs as $pair) {
            $parts = explode(':', $pair, 2);
            if (count($parts) === 2) {
                $result[] = [
                    'id' => $parts[0],
                    'name' => $parts[1]
                ];
            }
        }
        
        return $result;
    }
}
?>
