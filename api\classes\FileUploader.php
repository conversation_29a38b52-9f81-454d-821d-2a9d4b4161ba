<?php
/**
 * File Uploader Class
 * Handles file uploads with validation and security
 */

class FileUploader {
    private $db;
    private $allowedTypes;
    private $maxSize;
    private $uploadPath;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->allowedTypes = ALLOWED_FILE_TYPES;
        $this->maxSize = UPLOAD_MAX_SIZE;
        $this->uploadPath = UPLOAD_PATH;
    }
    
    /**
     * Upload files
     */
    public function upload($files, $type = 'general', $userId = null) {
        if (empty($files)) {
            throw new Exception("No files provided");
        }
        
        $uploadedFiles = [];
        
        // Handle single file or multiple files
        if (isset($files['file'])) {
            // Single file
            $uploadedFiles[] = $this->uploadSingleFile($files['file'], $type, $userId);
        } else {
            // Multiple files
            foreach ($files as $fileKey => $file) {
                if (is_array($file['name'])) {
                    // Multiple files with same input name
                    for ($i = 0; $i < count($file['name']); $i++) {
                        $singleFile = [
                            'name' => $file['name'][$i],
                            'type' => $file['type'][$i],
                            'tmp_name' => $file['tmp_name'][$i],
                            'error' => $file['error'][$i],
                            'size' => $file['size'][$i]
                        ];
                        $uploadedFiles[] = $this->uploadSingleFile($singleFile, $type, $userId);
                    }
                } else {
                    // Single file
                    $uploadedFiles[] = $this->uploadSingleFile($file, $type, $userId);
                }
            }
        }
        
        return [
            'files' => $uploadedFiles,
            'count' => count($uploadedFiles)
        ];
    }
    
    /**
     * Upload single file
     */
    private function uploadSingleFile($file, $type, $userId) {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception($this->getUploadErrorMessage($file['error']));
        }
        
        // Validate file size
        if ($file['size'] > $this->maxSize) {
            throw new Exception("File size exceeds maximum allowed size of " . ($this->maxSize / 1024 / 1024) . "MB");
        }
        
        // Validate file type
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, $this->allowedTypes)) {
            throw new Exception("File type not allowed. Allowed types: " . implode(', ', $this->allowedTypes));
        }
        
        // Validate MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!$this->isValidMimeType($mimeType, $fileExtension)) {
            throw new Exception("Invalid file type detected");
        }
        
        // Generate unique filename
        $originalName = pathinfo($file['name'], PATHINFO_FILENAME);
        $sanitizedName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $originalName);
        $uniqueId = uniqid();
        $fileName = $sanitizedName . '_' . $uniqueId . '.' . $fileExtension;
        
        // Determine upload directory based on type
        $typeDir = $this->getTypeDirectory($type);
        $uploadDir = $this->uploadPath . $typeDir . '/';
        
        // Create directory if it doesn't exist
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $filePath = $uploadDir . $fileName;
        $relativeUrl = $typeDir . '/' . $fileName;
        
        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            throw new Exception("Failed to move uploaded file");
        }
        
        // Set proper permissions
        chmod($filePath, 0644);
        
        // Log upload activity
        if ($userId) {
            log_activity($userId, 'file_uploaded', 'file', null, "File uploaded: $fileName");
        }
        
        return [
            'original_name' => $file['name'],
            'file_name' => $fileName,
            'file_path' => $relativeUrl,
            'file_url' => UPLOAD_URL . $relativeUrl,
            'file_size' => $file['size'],
            'file_type' => $fileExtension,
            'mime_type' => $mimeType,
            'upload_type' => $type,
            'uploaded_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Get type-specific directory
     */
    private function getTypeDirectory($type) {
        switch ($type) {
            case 'profile':
                return 'profiles';
            case 'document':
            case 'cida':
            case 'license':
                return 'documents';
            case 'portfolio':
                return 'portfolios';
            case 'project':
                return 'projects';
            case 'review':
                return 'reviews';
            default:
                return 'general';
        }
    }
    
    /**
     * Validate MIME type
     */
    private function isValidMimeType($mimeType, $extension) {
        $validMimeTypes = [
            'jpg' => ['image/jpeg'],
            'jpeg' => ['image/jpeg'],
            'png' => ['image/png'],
            'gif' => ['image/gif'],
            'pdf' => ['application/pdf'],
            'doc' => ['application/msword'],
            'docx' => ['application/vnd.openxmlformats-officedocument.wordprocessingml.document']
        ];
        
        if (!isset($validMimeTypes[$extension])) {
            return false;
        }
        
        return in_array($mimeType, $validMimeTypes[$extension]);
    }
    
    /**
     * Get upload error message
     */
    private function getUploadErrorMessage($errorCode) {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
                return "File exceeds the upload_max_filesize directive in php.ini";
            case UPLOAD_ERR_FORM_SIZE:
                return "File exceeds the MAX_FILE_SIZE directive in the HTML form";
            case UPLOAD_ERR_PARTIAL:
                return "File was only partially uploaded";
            case UPLOAD_ERR_NO_FILE:
                return "No file was uploaded";
            case UPLOAD_ERR_NO_TMP_DIR:
                return "Missing a temporary folder";
            case UPLOAD_ERR_CANT_WRITE:
                return "Failed to write file to disk";
            case UPLOAD_ERR_EXTENSION:
                return "File upload stopped by extension";
            default:
                return "Unknown upload error";
        }
    }
    
    /**
     * Delete file
     */
    public function deleteFile($filePath, $userId = null) {
        $fullPath = $this->uploadPath . $filePath;
        
        if (!file_exists($fullPath)) {
            throw new Exception("File not found");
        }
        
        if (!unlink($fullPath)) {
            throw new Exception("Failed to delete file");
        }
        
        // Log deletion activity
        if ($userId) {
            log_activity($userId, 'file_deleted', 'file', null, "File deleted: $filePath");
        }
        
        return true;
    }
    
    /**
     * Get file info
     */
    public function getFileInfo($filePath) {
        $fullPath = $this->uploadPath . $filePath;
        
        if (!file_exists($fullPath)) {
            throw new Exception("File not found");
        }
        
        $fileInfo = [
            'file_path' => $filePath,
            'file_url' => UPLOAD_URL . $filePath,
            'file_size' => filesize($fullPath),
            'file_type' => pathinfo($fullPath, PATHINFO_EXTENSION),
            'mime_type' => mime_content_type($fullPath),
            'created_at' => date('Y-m-d H:i:s', filectime($fullPath)),
            'modified_at' => date('Y-m-d H:i:s', filemtime($fullPath))
        ];
        
        return $fileInfo;
    }
    
    /**
     * Resize image (for profile pictures and thumbnails)
     */
    public function resizeImage($filePath, $maxWidth = 800, $maxHeight = 600, $quality = 85) {
        $fullPath = $this->uploadPath . $filePath;
        
        if (!file_exists($fullPath)) {
            throw new Exception("File not found");
        }
        
        $imageInfo = getimagesize($fullPath);
        if (!$imageInfo) {
            throw new Exception("Invalid image file");
        }
        
        $originalWidth = $imageInfo[0];
        $originalHeight = $imageInfo[1];
        $imageType = $imageInfo[2];
        
        // Calculate new dimensions
        $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);
        $newWidth = round($originalWidth * $ratio);
        $newHeight = round($originalHeight * $ratio);
        
        // Create image resource
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                $sourceImage = imagecreatefromjpeg($fullPath);
                break;
            case IMAGETYPE_PNG:
                $sourceImage = imagecreatefrompng($fullPath);
                break;
            case IMAGETYPE_GIF:
                $sourceImage = imagecreatefromgif($fullPath);
                break;
            default:
                throw new Exception("Unsupported image type");
        }
        
        // Create new image
        $newImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG and GIF
        if ($imageType == IMAGETYPE_PNG || $imageType == IMAGETYPE_GIF) {
            imagealphablending($newImage, false);
            imagesavealpha($newImage, true);
            $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
            imagefilledrectangle($newImage, 0, 0, $newWidth, $newHeight, $transparent);
        }
        
        // Resize image
        imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
        
        // Generate resized filename
        $pathInfo = pathinfo($fullPath);
        $resizedPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_resized.' . $pathInfo['extension'];
        $resizedRelativePath = str_replace($this->uploadPath, '', $resizedPath);
        
        // Save resized image
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                imagejpeg($newImage, $resizedPath, $quality);
                break;
            case IMAGETYPE_PNG:
                imagepng($newImage, $resizedPath, 9);
                break;
            case IMAGETYPE_GIF:
                imagegif($newImage, $resizedPath);
                break;
        }
        
        // Clean up memory
        imagedestroy($sourceImage);
        imagedestroy($newImage);
        
        return [
            'original_path' => $filePath,
            'resized_path' => $resizedRelativePath,
            'resized_url' => UPLOAD_URL . $resizedRelativePath,
            'original_size' => ['width' => $originalWidth, 'height' => $originalHeight],
            'new_size' => ['width' => $newWidth, 'height' => $newHeight]
        ];
    }
    
    /**
     * Clean up old temporary files
     */
    public function cleanupOldFiles($days = 30) {
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        $deletedCount = 0;
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->uploadPath),
            RecursiveIteratorIterator::LEAVES_ONLY
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getMTime() < $cutoffTime) {
                if (unlink($file->getPathname())) {
                    $deletedCount++;
                }
            }
        }
        
        return $deletedCount;
    }
}
?>
