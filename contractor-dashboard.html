<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Contractor Dashboard - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="contractor dashboard, construction, Sri Lanka" name="keywords">
    <meta content="Contractor Dashboard - Brick & Click" name="description">

    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border position-relative text-primary" style="width: 6rem; height: 6rem;" role="status"></div>
        <img class="position-absolute top-50 start-50 translate-middle" src="img/icons/icon-1.png" alt="Icon">
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5 wow fadeIn" data-wow-delay="0.1s">
        <a href="index.html" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="contractor-dashboard.html" class="nav-item nav-link active">Dashboard</a>
                <a href="contractor-quotes.html" class="nav-item nav-link">Quote Requests</a>
                <a href="contractor-projects.html" class="nav-item nav-link">My Projects</a>
                <a href="contractor-portfolio.html" class="nav-item nav-link">Portfolio</a>
                <a href="contractor-reviews.html" class="nav-item nav-link">Reviews</a>
            </div>
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                    <i class="fa fa-user me-2"></i><span id="userName">Loading...</span>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="contractor-profile.html"><i class="fa fa-user me-2"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="notifications.html"><i class="fa fa-bell me-2"></i>Notifications <span class="badge bg-danger" id="notificationCount">0</span></a></li>
                    <li><a class="dropdown-item" href="settings.html"><i class="fa fa-cog me-2"></i>Settings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Page Header Start -->
    <div class="container-fluid page-header py-5 mb-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <h1 class="display-1 text-white animated slideInDown">Contractor Dashboard</h1>
            <nav aria-label="breadcrumb animated slideInDown">
                <ol class="breadcrumb text-uppercase mb-0">
                    <li class="breadcrumb-item"><a class="text-white" href="index.html">Home</a></li>
                    <li class="breadcrumb-item text-primary active" aria-current="page">Dashboard</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Dashboard Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <!-- Welcome Section -->
            <div class="row mb-5">
                <div class="col-12">
                    <div class="bg-light rounded p-4">
                        <div class="row align-items-center">
                            <div class="col-lg-8">
                                <h3 class="text-primary mb-2">Welcome back, <span id="businessName">Loading...</span>!</h3>
                                <p class="mb-0">Manage your construction business, respond to quotes, and track your projects.</p>
                            </div>
                            <div class="col-lg-4 text-end">
                                <div class="verification-status" id="verificationStatus">
                                    <span class="badge bg-warning">Verification Pending</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row g-4 mb-5">
                <div class="col-lg-3 col-md-6">
                    <div class="bg-white rounded shadow-sm p-4 text-center">
                        <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fa fa-file-alt text-white"></i>
                        </div>
                        <h4 class="text-primary mb-1" id="totalQuotes">0</h4>
                        <p class="mb-0">Total Quote Requests</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="bg-white rounded shadow-sm p-4 text-center">
                        <div class="bg-success rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fa fa-check-circle text-white"></i>
                        </div>
                        <h4 class="text-success mb-1" id="acceptedQuotes">0</h4>
                        <p class="mb-0">Accepted Quotes</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="bg-white rounded shadow-sm p-4 text-center">
                        <div class="bg-warning rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fa fa-tools text-white"></i>
                        </div>
                        <h4 class="text-warning mb-1" id="activeProjects">0</h4>
                        <p class="mb-0">Active Projects</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="bg-white rounded shadow-sm p-4 text-center">
                        <div class="bg-info rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fa fa-star text-white"></i>
                        </div>
                        <h4 class="text-info mb-1" id="averageRating">0.0</h4>
                        <p class="mb-0">Average Rating</p>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="row g-4">
                <!-- Recent Quote Requests -->
                <div class="col-lg-8">
                    <div class="bg-white rounded shadow-sm p-4 mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="text-primary mb-0">
                                <i class="fa fa-file-alt me-2"></i>Recent Quote Requests
                            </h5>
                            <a href="contractor-quotes.html" class="btn btn-outline-primary btn-sm">View All</a>
                        </div>
                        <div id="recentQuotes">
                            <div class="text-center py-4">
                                <i class="fa fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="text-muted mt-2">Loading quote requests...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Active Projects -->
                    <div class="bg-white rounded shadow-sm p-4">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="text-primary mb-0">
                                <i class="fa fa-tools me-2"></i>Active Projects
                            </h5>
                            <a href="contractor-projects.html" class="btn btn-outline-primary btn-sm">View All</a>
                        </div>
                        <div id="activeProjectsList">
                            <div class="text-center py-4">
                                <i class="fa fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="text-muted mt-2">Loading active projects...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Quick Actions -->
                    <div class="bg-white rounded shadow-sm p-4 mb-4">
                        <h5 class="text-primary mb-4">
                            <i class="fa fa-bolt me-2"></i>Quick Actions
                        </h5>
                        <div class="d-grid gap-2">
                            <a href="contractor-profile.html" class="btn btn-outline-primary">
                                <i class="fa fa-user me-2"></i>Update Profile
                            </a>
                            <a href="contractor-portfolio.html" class="btn btn-outline-primary">
                                <i class="fa fa-images me-2"></i>Manage Portfolio
                            </a>
                            <a href="contractor-quotes.html" class="btn btn-outline-primary">
                                <i class="fa fa-file-alt me-2"></i>View Quote Requests
                            </a>
                            <a href="contractor-reviews.html" class="btn btn-outline-primary">
                                <i class="fa fa-star me-2"></i>View Reviews
                            </a>
                        </div>
                    </div>

                    <!-- Recent Reviews -->
                    <div class="bg-white rounded shadow-sm p-4 mb-4">
                        <h5 class="text-primary mb-4">
                            <i class="fa fa-star me-2"></i>Recent Reviews
                        </h5>
                        <div id="recentReviews">
                            <div class="text-center py-4">
                                <i class="fa fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="text-muted mt-2">Loading reviews...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Metrics -->
                    <div class="bg-white rounded shadow-sm p-4">
                        <h5 class="text-primary mb-4">
                            <i class="fa fa-chart-line me-2"></i>Performance
                        </h5>
                        <div class="performance-metrics">
                            <div class="d-flex justify-content-between mb-3">
                                <span>Response Rate</span>
                                <strong id="responseRate">0%</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>Project Completion</span>
                                <strong id="completionRate">0%</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>Customer Satisfaction</span>
                                <strong id="satisfactionRate">0%</strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Total Earnings</span>
                                <strong id="totalEarnings">LKR 0</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Dashboard End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-body footer mt-5 pt-5 px-0 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Contact Us</h3>
                    <p class="mb-2"><i class="fa fa-map-marker-alt text-primary me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt text-primary me-3"></i>+94 77 123 4567</p>
                    <p class="mb-2"><i class="fa fa-envelope text-primary me-3"></i><EMAIL></p>
                    <div class="d-flex pt-2">
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-twitter"></i></a>
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-facebook-f"></i></a>
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-youtube"></i></a>
                        <a class="btn btn-square btn-outline-body me-0" href=""><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Quick Links</h3>
                    <a class="btn btn-link" href="about.html">About Us</a>
                    <a class="btn btn-link" href="contact.html">Contact Us</a>
                    <a class="btn btn-link" href="service.html">Our Services</a>
                    <a class="btn btn-link" href="">Terms & Condition</a>
                    <a class="btn btn-link" href="">Support</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">For Contractors</h3>
                    <a class="btn btn-link" href="contractor-dashboard.html">Dashboard</a>
                    <a class="btn btn-link" href="contractor-quotes.html">Quote Requests</a>
                    <a class="btn btn-link" href="contractor-projects.html">My Projects</a>
                    <a class="btn btn-link" href="contractor-portfolio.html">Portfolio</a>
                    <a class="btn btn-link" href="contractor-reviews.html">Reviews</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Newsletter</h3>
                    <p>Stay updated with the latest construction news and opportunities.</p>
                    <div class="position-relative mx-auto" style="max-width: 400px;">
                        <input class="form-control bg-transparent w-100 py-3 ps-4 pe-5" type="text" placeholder="Your email">
                        <button type="button" class="btn btn-primary py-2 position-absolute top-0 end-0 mt-2 me-2">SignUp</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid copyright">
            <div class="container">
                <div class="row">
                    <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                        &copy; <a href="#">Brick & Click</a>, All Right Reserved.
                    </div>
                    <div class="col-md-6 text-center text-md-end">
                        Designed By <a href="https://htmlcodex.com">HTML Codex</a>
                        <br> Distributed By: <a class="border-bottom" href="https://themewagon.com" target="_blank">ThemeWagon</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>

    <!-- Template Javascript -->
    <script src="js/main.js"></script>

    <!-- Contractor Dashboard Javascript -->
    <script>
        // API Configuration
        const API_BASE_URL = 'api';
        let currentUser = null;

        // API call helper
        async function apiCall(endpoint, method = 'GET', data = null) {
            const config = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include'
            };

            if (data) {
                config.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(`${API_BASE_URL}/${endpoint}`, config);
                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.error || 'An error occurred');
                }

                return result;
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        }

        // Check authentication
        function checkAuth() {
            const user = localStorage.getItem('user');
            const loginTime = localStorage.getItem('loginTime');

            if (!user || !loginTime) {
                window.location.href = 'login.html';
                return false;
            }

            const userData = JSON.parse(user);
            const timeDiff = Date.now() - parseInt(loginTime);
            const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds

            if (timeDiff > oneHour) {
                localStorage.removeItem('user');
                localStorage.removeItem('loginTime');
                window.location.href = 'login.html';
                return false;
            }

            if (userData.user_type !== 'contractor') {
                window.location.href = 'login.html';
                return false;
            }

            currentUser = userData;
            return true;
        }

        // Logout function
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                localStorage.removeItem('user');
                localStorage.removeItem('loginTime');

                // Call logout API
                apiCall('auth/logout', 'POST').catch(console.error);

                window.location.href = 'login.html';
            }
        }

        // Load dashboard data
        async function loadDashboardData() {
            try {
                // Load contractor profile
                const profileResponse = await apiCall('contractors/profile');
                if (profileResponse.success) {
                    const profile = profileResponse.data;
                    updateProfileInfo(profile);
                }

                // Load statistics (placeholder - implement in backend)
                loadStatistics();

                // Load recent quotes
                loadRecentQuotes();

                // Load active projects
                loadActiveProjects();

                // Load recent reviews
                loadRecentReviews();

                // Load notifications count
                loadNotificationsCount();

            } catch (error) {
                console.error('Error loading dashboard data:', error);
                showError('Failed to load dashboard data');
            }
        }

        // Update profile information
        function updateProfileInfo(profile) {
            document.getElementById('userName').textContent = profile.first_name + ' ' + profile.last_name;
            document.getElementById('businessName').textContent = profile.business_name;

            // Update verification status
            const statusElement = document.getElementById('verificationStatus');
            const status = profile.verification_status;

            statusElement.innerHTML = '';

            switch (status) {
                case 'approved':
                    statusElement.innerHTML = '<span class="badge bg-success"><i class="fa fa-check me-1"></i>Verified</span>';
                    break;
                case 'pending':
                    statusElement.innerHTML = '<span class="badge bg-warning"><i class="fa fa-clock me-1"></i>Verification Pending</span>';
                    break;
                case 'rejected':
                    statusElement.innerHTML = '<span class="badge bg-danger"><i class="fa fa-times me-1"></i>Verification Rejected</span>';
                    break;
                default:
                    statusElement.innerHTML = '<span class="badge bg-secondary">Unknown Status</span>';
            }
        }

        // Load statistics
        function loadStatistics() {
            // Placeholder data - implement actual API calls
            document.getElementById('totalQuotes').textContent = '12';
            document.getElementById('acceptedQuotes').textContent = '8';
            document.getElementById('activeProjects').textContent = '3';
            document.getElementById('averageRating').textContent = '4.8';

            // Performance metrics
            document.getElementById('responseRate').textContent = '95%';
            document.getElementById('completionRate').textContent = '88%';
            document.getElementById('satisfactionRate').textContent = '92%';
            document.getElementById('totalEarnings').textContent = 'LKR 2,450,000';
        }

        // Load recent quotes
        function loadRecentQuotes() {
            const quotesContainer = document.getElementById('recentQuotes');

            // Placeholder data
            const quotes = [
                {
                    id: 1,
                    title: 'Two-story house construction',
                    customer: 'John Doe',
                    location: 'Colombo',
                    budget: '5,000,000',
                    date: '2025-01-18',
                    status: 'pending'
                },
                {
                    id: 2,
                    title: 'Kitchen renovation',
                    customer: 'Jane Smith',
                    location: 'Gampaha',
                    budget: '800,000',
                    date: '2025-01-17',
                    status: 'pending'
                }
            ];

            if (quotes.length === 0) {
                quotesContainer.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fa fa-file-alt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent quote requests</p>
                    </div>
                `;
                return;
            }

            quotesContainer.innerHTML = quotes.map(quote => `
                <div class="border-bottom pb-3 mb-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">${quote.title}</h6>
                            <p class="text-muted mb-1">
                                <i class="fa fa-user me-1"></i>${quote.customer} •
                                <i class="fa fa-map-marker-alt me-1"></i>${quote.location}
                            </p>
                            <p class="text-muted mb-0">
                                <i class="fa fa-money-bill-wave me-1"></i>Budget: LKR ${quote.budget}
                            </p>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">${quote.date}</small>
                            <br>
                            <a href="contractor-quotes.html?id=${quote.id}" class="btn btn-primary btn-sm mt-1">View Details</a>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Load active projects
        function loadActiveProjects() {
            const projectsContainer = document.getElementById('activeProjectsList');

            // Placeholder data
            const projects = [
                {
                    id: 1,
                    name: 'Residential Complex - Phase 1',
                    customer: 'ABC Holdings',
                    progress: 65,
                    status: 'in_progress',
                    deadline: '2025-03-15'
                },
                {
                    id: 2,
                    name: 'Office Building Renovation',
                    customer: 'XYZ Company',
                    progress: 30,
                    status: 'in_progress',
                    deadline: '2025-02-28'
                }
            ];

            if (projects.length === 0) {
                projectsContainer.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fa fa-tools fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No active projects</p>
                    </div>
                `;
                return;
            }

            projectsContainer.innerHTML = projects.map(project => `
                <div class="border-bottom pb-3 mb-3">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <h6 class="mb-1">${project.name}</h6>
                            <p class="text-muted mb-1">
                                <i class="fa fa-user me-1"></i>${project.customer}
                            </p>
                            <p class="text-muted mb-0">
                                <i class="fa fa-calendar me-1"></i>Deadline: ${project.deadline}
                            </p>
                        </div>
                        <a href="contractor-projects.html?id=${project.id}" class="btn btn-outline-primary btn-sm">View</a>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar" role="progressbar" style="width: ${project.progress}%" aria-valuenow="${project.progress}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <small class="text-muted">${project.progress}% Complete</small>
                </div>
            `).join('');
        }

        // Load recent reviews
        function loadRecentReviews() {
            const reviewsContainer = document.getElementById('recentReviews');

            // Placeholder data
            const reviews = [
                {
                    customer: 'John Doe',
                    rating: 5,
                    comment: 'Excellent work quality and timely completion.',
                    date: '2025-01-15'
                },
                {
                    customer: 'Jane Smith',
                    rating: 4,
                    comment: 'Good service, professional team.',
                    date: '2025-01-10'
                }
            ];

            if (reviews.length === 0) {
                reviewsContainer.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fa fa-star fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No reviews yet</p>
                    </div>
                `;
                return;
            }

            reviewsContainer.innerHTML = reviews.map(review => `
                <div class="border-bottom pb-3 mb-3">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <strong>${review.customer}</strong>
                        <small class="text-muted">${review.date}</small>
                    </div>
                    <div class="text-warning mb-2">
                        ${Array(review.rating).fill('<i class="fa fa-star"></i>').join('')}
                        ${Array(5 - review.rating).fill('<i class="far fa-star"></i>').join('')}
                    </div>
                    <p class="mb-0 small">${review.comment}</p>
                </div>
            `).join('');
        }

        // Load notifications count
        async function loadNotificationsCount() {
            try {
                const response = await apiCall('notifications?unread_only=true&limit=1');
                if (response.success) {
                    const count = response.data.unread_count || 0;
                    const badge = document.getElementById('notificationCount');
                    if (count > 0) {
                        badge.textContent = count > 99 ? '99+' : count;
                        badge.style.display = 'inline';
                    } else {
                        badge.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('Error loading notifications count:', error);
            }
        }

        // Show error message
        function showError(message) {
            // Simple error display - you can enhance this
            console.error(message);
            alert(message);
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadDashboardData();

                // Refresh data every 5 minutes
                setInterval(loadDashboardData, 5 * 60 * 1000);
            }
        });
    </script>
</body>

</html>
