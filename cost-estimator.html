<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Cost Estimator - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="construction, contractors, Sri Lanka, cost estimator, project cost" name="keywords">
    <meta content="Construction Cost Estimator - Brick & Click" name="description">

    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border position-relative text-primary" style="width: 6rem; height: 6rem;" role="status"></div>
        <img class="position-absolute top-50 start-50 translate-middle" src="img/icons/icon-1.png" alt="Icon">
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5 wow fadeIn" data-wow-delay="0.1s">
        <a href="index.html" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="customer-dashboard.html" class="nav-item nav-link">Dashboard</a>
                <a href="search-contractors.html" class="nav-item nav-link">Find Contractors</a>
                <a href="my-quotes.html" class="nav-item nav-link">My Quotes</a>
                <a href="my-favorites.html" class="nav-item nav-link">Favorites</a>
                <a href="cost-estimator.html" class="nav-item nav-link active">Cost Estimator</a>
            </div>
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                    <i class="fa fa-user me-2"></i>John Doe
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="customer-profile.html"><i class="fa fa-user me-2"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="notifications.html"><i class="fa fa-bell me-2"></i>Notifications <span class="badge bg-danger">3</span></a></li>
                    <li><a class="dropdown-item" href="settings.html"><i class="fa fa-cog me-2"></i>Settings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="index.html"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Page Header Start -->
    <div class="container-fluid page-header py-5 mb-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <h1 class="display-1 text-white animated slideInDown">Cost Estimator</h1>
            <nav aria-label="breadcrumb animated slideInDown">
                <ol class="breadcrumb text-uppercase mb-0">
                    <li class="breadcrumb-item"><a class="text-white" href="customer-dashboard.html">Dashboard</a></li>
                    <li class="breadcrumb-item text-primary active" aria-current="page">Cost Estimator</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Cost Estimator Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="row">
                <!-- Estimator Form -->
                <div class="col-lg-8">
                    <div class="wow fadeInUp" data-wow-delay="0.1s">
                        <div class="bg-light rounded p-5">
                            <h3 class="text-primary mb-4">
                                <i class="fa fa-calculator me-2"></i>Project Cost Estimator
                            </h3>
                            <p class="mb-4">Get an estimated cost range for your construction project. This tool provides approximate costs based on current market rates in Sri Lanka.</p>

                            <form id="costEstimatorForm">
                                <!-- Project Type -->
                                <div class="row g-3 mb-4">
                                    <div class="col-12">
                                        <h5 class="text-primary">Project Type</h5>
                                        <hr>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="projectType" required>
                                                <option value="">Select Project Type</option>
                                                <option value="new-house">New House Construction</option>
                                                <option value="renovation">House Renovation</option>
                                                <option value="extension">House Extension</option>
                                                <option value="commercial">Commercial Building</option>
                                                <option value="apartment">Apartment Complex</option>
                                                <option value="warehouse">Warehouse/Factory</option>
                                            </select>
                                            <label for="projectType">Project Type</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="buildingType" required>
                                                <option value="">Select Building Type</option>
                                                <option value="basic">Basic/Standard</option>
                                                <option value="medium">Medium Quality</option>
                                                <option value="luxury">Luxury/High-end</option>
                                            </select>
                                            <label for="buildingType">Quality Level</label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Area and Dimensions -->
                                <div class="row g-3 mb-4">
                                    <div class="col-12">
                                        <h5 class="text-primary">Area & Dimensions</h5>
                                        <hr>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="number" class="form-control" id="floorArea" placeholder="Floor Area" min="1" required>
                                            <label for="floorArea">Floor Area (sq ft)</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="floors" required>
                                                <option value="">Number of Floors</option>
                                                <option value="1">1 Floor</option>
                                                <option value="2">2 Floors</option>
                                                <option value="3">3 Floors</option>
                                                <option value="4">4+ Floors</option>
                                            </select>
                                            <label for="floors">Number of Floors</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="number" class="form-control" id="bedrooms" placeholder="Bedrooms" min="0">
                                            <label for="bedrooms">Number of Bedrooms</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="number" class="form-control" id="bathrooms" placeholder="Bathrooms" min="0">
                                            <label for="bathrooms">Number of Bathrooms</label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Location -->
                                <div class="row g-3 mb-4">
                                    <div class="col-12">
                                        <h5 class="text-primary">Location</h5>
                                        <hr>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="district" required>
                                                <option value="">Select District</option>
                                                <option value="colombo">Colombo</option>
                                                <option value="gampaha">Gampaha</option>
                                                <option value="kalutara">Kalutara</option>
                                                <option value="kandy">Kandy</option>
                                                <option value="matale">Matale</option>
                                                <option value="nuwara-eliya">Nuwara Eliya</option>
                                                <option value="galle">Galle</option>
                                                <option value="matara">Matara</option>
                                                <option value="hambantota">Hambantota</option>
                                                <option value="jaffna">Jaffna</option>
                                                <option value="kurunegala">Kurunegala</option>
                                                <option value="anuradhapura">Anuradhapura</option>
                                                <option value="ratnapura">Ratnapura</option>
                                                <option value="kegalle">Kegalle</option>
                                            </select>
                                            <label for="district">District</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="accessibility">
                                                <option value="easy">Easy Access (Main Road)</option>
                                                <option value="moderate">Moderate Access</option>
                                                <option value="difficult">Difficult Access</option>
                                            </select>
                                            <label for="accessibility">Site Accessibility</label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Features -->
                                <div class="row g-3 mb-4">
                                    <div class="col-12">
                                        <h5 class="text-primary">Additional Features</h5>
                                        <hr>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="swimmingPool" value="pool">
                                            <label class="form-check-label" for="swimmingPool">Swimming Pool</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="garage" value="garage">
                                            <label class="form-check-label" for="garage">Garage</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="garden" value="garden">
                                            <label class="form-check-label" for="garden">Landscaped Garden</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="solarPower" value="solar">
                                            <label class="form-check-label" for="solarPower">Solar Power System</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="securitySystem" value="security">
                                            <label class="form-check-label" for="securitySystem">Security System</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="airConditioning" value="ac">
                                            <label class="form-check-label" for="airConditioning">Central Air Conditioning</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary py-3 px-5">
                                        <i class="fa fa-calculator me-2"></i>Calculate Estimate
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Results Panel -->
                <div class="col-lg-4">
                    <div class="wow fadeInUp" data-wow-delay="0.3s">
                        <!-- Cost Breakdown -->
                        <div class="bg-primary rounded p-4 mb-4 text-white" id="costResults" style="display: none;">
                            <h4 class="text-white mb-3">
                                <i class="fa fa-chart-pie me-2"></i>Cost Estimate
                            </h4>
                            <div class="cost-range mb-4">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Minimum Cost:</span>
                                    <strong id="minCost">-</strong>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Maximum Cost:</span>
                                    <strong id="maxCost">-</strong>
                                </div>
                                <hr class="my-3">
                                <div class="d-flex justify-content-between">
                                    <span class="h5">Average Cost:</span>
                                    <strong class="h5" id="avgCost">-</strong>
                                </div>
                            </div>
                            <small class="text-light">
                                <i class="fa fa-info-circle me-1"></i>
                                This is an approximate estimate. Actual costs may vary based on material prices, labor rates, and specific requirements.
                            </small>
                        </div>

                        <!-- Cost Breakdown Details -->
                        <div class="bg-light rounded p-4 mb-4" id="costBreakdown" style="display: none;">
                            <h5 class="text-primary mb-3">Cost Breakdown</h5>
                            <div class="breakdown-item d-flex justify-content-between mb-2">
                                <span>Foundation & Structure:</span>
                                <strong id="structureCost">-</strong>
                            </div>
                            <div class="breakdown-item d-flex justify-content-between mb-2">
                                <span>Walls & Roofing:</span>
                                <strong id="wallsCost">-</strong>
                            </div>
                            <div class="breakdown-item d-flex justify-content-between mb-2">
                                <span>Electrical & Plumbing:</span>
                                <strong id="utilitiesCost">-</strong>
                            </div>
                            <div class="breakdown-item d-flex justify-content-between mb-2">
                                <span>Finishing & Interior:</span>
                                <strong id="finishingCost">-</strong>
                            </div>
                            <div class="breakdown-item d-flex justify-content-between mb-2">
                                <span>Additional Features:</span>
                                <strong id="featuresCost">-</strong>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between">
                                <strong>Total Estimated Cost:</strong>
                                <strong class="text-primary" id="totalCost">-</strong>
                            </div>
                        </div>

                        <!-- Tips -->
                        <div class="bg-light rounded p-4">
                            <h5 class="text-primary mb-3">
                                <i class="fa fa-lightbulb me-2"></i>Cost Saving Tips
                            </h5>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fa fa-check text-success me-2"></i>
                                    Get multiple quotes from verified contractors
                                </li>
                                <li class="mb-2">
                                    <i class="fa fa-check text-success me-2"></i>
                                    Plan your project during off-peak seasons
                                </li>
                                <li class="mb-2">
                                    <i class="fa fa-check text-success me-2"></i>
                                    Consider local materials to reduce costs
                                </li>
                                <li class="mb-2">
                                    <i class="fa fa-check text-success me-2"></i>
                                    Phase your project if budget is limited
                                </li>
                                <li class="mb-0">
                                    <i class="fa fa-check text-success me-2"></i>
                                    Work with CIDA-certified contractors
                                </li>
                            </ul>

                            <div class="mt-4">
                                <a href="search-contractors.html" class="btn btn-primary w-100">
                                    <i class="fa fa-search me-2"></i>Find Contractors
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Cost Estimator End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-body footer mt-5 pt-5 px-0 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Contact Us</h3>
                    <p class="mb-2"><i class="fa fa-map-marker-alt text-primary me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt text-primary me-3"></i>+94 77 123 4567</p>
                    <p class="mb-2"><i class="fa fa-envelope text-primary me-3"></i><EMAIL></p>
                    <div class="d-flex pt-2">
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-twitter"></i></a>
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-facebook-f"></i></a>
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-youtube"></i></a>
                        <a class="btn btn-square btn-outline-body me-0" href=""><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Quick Links</h3>
                    <a class="btn btn-link" href="about.html">About Us</a>
                    <a class="btn btn-link" href="contact.html">Contact Us</a>
                    <a class="btn btn-link" href="service.html">Our Services</a>
                    <a class="btn btn-link" href="">Terms & Condition</a>
                    <a class="btn btn-link" href="">Support</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">For Contractors</h3>
                    <a class="btn btn-link" href="register.html">Join as Contractor</a>
                    <a class="btn btn-link" href="">Contractor Dashboard</a>
                    <a class="btn btn-link" href="">Success Stories</a>
                    <a class="btn btn-link" href="">Contractor Resources</a>
                    <a class="btn btn-link" href="">FAQ</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Newsletter</h3>
                    <p>Stay updated with the latest construction news and opportunities.</p>
                    <div class="position-relative mx-auto" style="max-width: 400px;">
                        <input class="form-control bg-transparent w-100 py-3 ps-4 pe-5" type="text" placeholder="Your email">
                        <button type="button" class="btn btn-primary py-2 position-absolute top-0 end-0 mt-2 me-2">SignUp</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid copyright">
            <div class="container">
                <div class="row">
                    <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                        &copy; <a href="#">Brick & Click</a>, All Right Reserved.
                    </div>
                    <div class="col-md-6 text-center text-md-end">
                        Designed By <a href="https://htmlcodex.com">HTML Codex</a>
                        <br> Distributed By: <a class="border-bottom" href="https://themewagon.com" target="_blank">ThemeWagon</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>

    <!-- Template Javascript -->
    <script src="js/main.js"></script>

    <!-- Cost Estimator Javascript -->
    <script>
        // Cost calculation rates (per sq ft in LKR)
        const costRates = {
            projectTypes: {
                'new-house': {
                    basic: { min: 8000, max: 12000 },
                    medium: { min: 12000, max: 18000 },
                    luxury: { min: 18000, max: 30000 }
                },
                'renovation': {
                    basic: { min: 4000, max: 7000 },
                    medium: { min: 7000, max: 12000 },
                    luxury: { min: 12000, max: 20000 }
                },
                'extension': {
                    basic: { min: 6000, max: 10000 },
                    medium: { min: 10000, max: 15000 },
                    luxury: { min: 15000, max: 25000 }
                },
                'commercial': {
                    basic: { min: 10000, max: 15000 },
                    medium: { min: 15000, max: 22000 },
                    luxury: { min: 22000, max: 35000 }
                },
                'apartment': {
                    basic: { min: 9000, max: 14000 },
                    medium: { min: 14000, max: 20000 },
                    luxury: { min: 20000, max: 32000 }
                },
                'warehouse': {
                    basic: { min: 5000, max: 8000 },
                    medium: { min: 8000, max: 12000 },
                    luxury: { min: 12000, max: 18000 }
                }
            },
            locationMultipliers: {
                'colombo': 1.2,
                'gampaha': 1.1,
                'kalutara': 1.0,
                'kandy': 0.95,
                'matale': 0.85,
                'nuwara-eliya': 0.9,
                'galle': 0.95,
                'matara': 0.9,
                'hambantota': 0.85,
                'jaffna': 0.8,
                'kurunegala': 0.85,
                'anuradhapura': 0.8,
                'ratnapura': 0.85,
                'kegalle': 0.85
            },
            accessibilityMultipliers: {
                'easy': 1.0,
                'moderate': 1.1,
                'difficult': 1.25
            },
            floorMultipliers: {
                '1': 1.0,
                '2': 1.15,
                '3': 1.25,
                '4': 1.35
            },
            additionalFeatures: {
                'pool': 1500000,
                'garage': 300000,
                'garden': 200000,
                'solar': 800000,
                'security': 150000,
                'ac': 500000
            }
        };

        document.getElementById('costEstimatorForm').addEventListener('submit', function(e) {
            e.preventDefault();
            calculateCost();
        });

        function calculateCost() {
            // Get form values
            const projectType = document.getElementById('projectType').value;
            const buildingType = document.getElementById('buildingType').value;
            const floorArea = parseInt(document.getElementById('floorArea').value);
            const floors = document.getElementById('floors').value;
            const district = document.getElementById('district').value;
            const accessibility = document.getElementById('accessibility').value;

            // Validate required fields
            if (!projectType || !buildingType || !floorArea || !floors || !district) {
                alert('Please fill in all required fields');
                return;
            }

            // Get base rates
            const baseRates = costRates.projectTypes[projectType][buildingType];

            // Calculate base cost
            let minCost = baseRates.min * floorArea;
            let maxCost = baseRates.max * floorArea;

            // Apply location multiplier
            const locationMultiplier = costRates.locationMultipliers[district] || 1.0;
            minCost *= locationMultiplier;
            maxCost *= locationMultiplier;

            // Apply accessibility multiplier
            const accessibilityMultiplier = costRates.accessibilityMultipliers[accessibility] || 1.0;
            minCost *= accessibilityMultiplier;
            maxCost *= accessibilityMultiplier;

            // Apply floor multiplier
            const floorMultiplier = costRates.floorMultipliers[floors] || 1.0;
            minCost *= floorMultiplier;
            maxCost *= floorMultiplier;

            // Add additional features cost
            let additionalCost = 0;
            const checkedFeatures = document.querySelectorAll('input[type="checkbox"]:checked');
            checkedFeatures.forEach(feature => {
                additionalCost += costRates.additionalFeatures[feature.value] || 0;
            });

            minCost += additionalCost;
            maxCost += additionalCost;

            // Calculate average
            const avgCost = (minCost + maxCost) / 2;

            // Display results
            displayResults(minCost, maxCost, avgCost, floorArea, additionalCost);
        }

        function displayResults(minCost, maxCost, avgCost, floorArea, additionalCost) {
            // Show results panels
            document.getElementById('costResults').style.display = 'block';
            document.getElementById('costBreakdown').style.display = 'block';

            // Format currency
            const formatCurrency = (amount) => {
                return 'LKR ' + amount.toLocaleString('en-US');
            };

            // Update main results
            document.getElementById('minCost').textContent = formatCurrency(Math.round(minCost));
            document.getElementById('maxCost').textContent = formatCurrency(Math.round(maxCost));
            document.getElementById('avgCost').textContent = formatCurrency(Math.round(avgCost));

            // Calculate breakdown (approximate percentages)
            const structureCost = avgCost * 0.35;
            const wallsCost = avgCost * 0.25;
            const utilitiesCost = avgCost * 0.20;
            const finishingCost = avgCost * 0.15;
            const featuresCost = additionalCost;

            // Update breakdown
            document.getElementById('structureCost').textContent = formatCurrency(Math.round(structureCost));
            document.getElementById('wallsCost').textContent = formatCurrency(Math.round(wallsCost));
            document.getElementById('utilitiesCost').textContent = formatCurrency(Math.round(utilitiesCost));
            document.getElementById('finishingCost').textContent = formatCurrency(Math.round(finishingCost));
            document.getElementById('featuresCost').textContent = formatCurrency(Math.round(featuresCost));
            document.getElementById('totalCost').textContent = formatCurrency(Math.round(avgCost));

            // Scroll to results
            document.getElementById('costResults').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // Save calculation to localStorage for potential quote requests
            const calculationData = {
                timestamp: new Date().toISOString(),
                projectType: document.getElementById('projectType').value,
                buildingType: document.getElementById('buildingType').value,
                floorArea: document.getElementById('floorArea').value,
                floors: document.getElementById('floors').value,
                district: document.getElementById('district').value,
                bedrooms: document.getElementById('bedrooms').value,
                bathrooms: document.getElementById('bathrooms').value,
                estimatedCost: {
                    min: Math.round(minCost),
                    max: Math.round(maxCost),
                    avg: Math.round(avgCost)
                }
            };

            localStorage.setItem('lastCostEstimate', JSON.stringify(calculationData));
        }

        // Auto-calculate when key fields change
        document.getElementById('projectType').addEventListener('change', function() {
            updateBuildingTypeOptions();
        });

        function updateBuildingTypeOptions() {
            const projectType = document.getElementById('projectType').value;
            const buildingTypeSelect = document.getElementById('buildingType');

            // Reset building type
            buildingTypeSelect.value = '';

            // Update labels based on project type
            const options = buildingTypeSelect.querySelectorAll('option');
            if (projectType === 'warehouse' || projectType === 'commercial') {
                options[1].textContent = 'Basic/Functional';
                options[2].textContent = 'Standard Quality';
                options[3].textContent = 'Premium/High-spec';
            } else {
                options[1].textContent = 'Basic/Standard';
                options[2].textContent = 'Medium Quality';
                options[3].textContent = 'Luxury/High-end';
            }
        }

        // Form validation and user experience improvements
        document.getElementById('floorArea').addEventListener('input', function() {
            const value = parseInt(this.value);
            if (value > 0) {
                // Auto-suggest bedrooms and bathrooms based on floor area
                const bedrooms = document.getElementById('bedrooms');
                const bathrooms = document.getElementById('bathrooms');

                if (!bedrooms.value) {
                    if (value < 800) bedrooms.value = 2;
                    else if (value < 1200) bedrooms.value = 3;
                    else if (value < 1800) bedrooms.value = 4;
                    else bedrooms.value = 5;
                }

                if (!bathrooms.value) {
                    if (value < 800) bathrooms.value = 1;
                    else if (value < 1200) bathrooms.value = 2;
                    else if (value < 1800) bathrooms.value = 3;
                    else bathrooms.value = 4;
                }
            }
        });

        // Load previous calculation if available
        document.addEventListener('DOMContentLoaded', function() {
            const lastEstimate = localStorage.getItem('lastCostEstimate');
            if (lastEstimate) {
                const data = JSON.parse(lastEstimate);

                // Check if calculation is recent (within 24 hours)
                const calculationTime = new Date(data.timestamp);
                const now = new Date();
                const hoursDiff = (now - calculationTime) / (1000 * 60 * 60);

                if (hoursDiff < 24) {
                    // Show option to load previous calculation
                    const loadPrevious = confirm('Would you like to load your previous cost calculation?');
                    if (loadPrevious) {
                        document.getElementById('projectType').value = data.projectType;
                        document.getElementById('buildingType').value = data.buildingType;
                        document.getElementById('floorArea').value = data.floorArea;
                        document.getElementById('floors').value = data.floors;
                        document.getElementById('district').value = data.district;
                        document.getElementById('bedrooms').value = data.bedrooms;
                        document.getElementById('bathrooms').value = data.bathrooms;

                        // Recalculate
                        calculateCost();
                    }
                }
            }
        });
    </script>

    <style>
        .cost-range {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
        }

        .breakdown-item {
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .breakdown-item:last-child {
            border-bottom: none;
        }

        #costResults {
            animation: slideInRight 0.5s ease;
        }

        #costBreakdown {
            animation: slideInRight 0.7s ease;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .form-floating > .form-select {
            padding-top: 1.625rem;
            padding-bottom: 0.625rem;
        }

        .form-check-input:checked {
            background-color: var(--accent-orange);
            border-color: var(--accent-orange);
        }

        @media (max-width: 768px) {
            .cost-range {
                padding: 15px;
            }

            #costResults, #costBreakdown {
                margin-top: 2rem;
            }
        }
    </style>
</body>

</html>
