<?php
/**
 * Cost Estimator Class
 * Calculates construction project cost estimates
 */

class CostEstimator {
    private $db;
    private $rates;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->rates = COST_RATES;
    }
    
    /**
     * Calculate cost estimate
     */
    public function calculate($data) {
        // Validate required fields
        $requiredFields = ['project_type', 'building_type', 'floor_area', 'floors', 'district'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                throw new Exception("Missing required field: $field");
            }
        }
        
        // Validate numeric fields
        $floorArea = intval($data['floor_area']);
        $floors = intval($data['floors']);
        
        if ($floorArea <= 0) {
            throw new Exception("Floor area must be greater than 0");
        }
        
        if ($floors <= 0) {
            throw new Exception("Number of floors must be greater than 0");
        }
        
        // Get base rates
        $projectType = $data['project_type'];
        $buildingType = $data['building_type'];
        
        if (!isset($this->rates['project_types'][$projectType][$buildingType])) {
            throw new Exception("Invalid project type or building type");
        }
        
        $baseRates = $this->rates['project_types'][$projectType][$buildingType];
        
        // Calculate base cost
        $minCost = $baseRates['min'] * $floorArea;
        $maxCost = $baseRates['max'] * $floorArea;
        
        // Apply location multiplier
        $district = strtolower($data['district']);
        $locationMultiplier = $this->rates['location_multipliers'][$district] ?? $this->rates['location_multipliers']['default'];
        
        $minCost *= $locationMultiplier;
        $maxCost *= $locationMultiplier;
        
        // Apply floor multiplier
        $floorMultiplier = $this->getFloorMultiplier($floors);
        $minCost *= $floorMultiplier;
        $maxCost *= $floorMultiplier;
        
        // Apply accessibility multiplier
        $accessibilityMultiplier = $this->getAccessibilityMultiplier($data['accessibility'] ?? 'easy');
        $minCost *= $accessibilityMultiplier;
        $maxCost *= $accessibilityMultiplier;
        
        // Add additional features cost
        $additionalCost = $this->calculateAdditionalFeatures($data['additional_features'] ?? []);
        $minCost += $additionalCost;
        $maxCost += $additionalCost;
        
        // Calculate average
        $avgCost = ($minCost + $maxCost) / 2;
        
        // Round to nearest thousand
        $minCost = round($minCost / 1000) * 1000;
        $maxCost = round($maxCost / 1000) * 1000;
        $avgCost = round($avgCost / 1000) * 1000;
        
        // Calculate breakdown
        $breakdown = $this->calculateBreakdown($avgCost, $additionalCost);
        
        // Prepare result
        $result = [
            'project_details' => [
                'project_type' => $projectType,
                'building_type' => $buildingType,
                'floor_area' => $floorArea,
                'floors' => $floors,
                'district' => $data['district'],
                'accessibility' => $data['accessibility'] ?? 'easy',
                'additional_features' => $data['additional_features'] ?? []
            ],
            'cost_estimate' => [
                'min_cost' => $minCost,
                'max_cost' => $maxCost,
                'avg_cost' => $avgCost,
                'currency' => 'LKR',
                'per_sqft_min' => round($minCost / $floorArea),
                'per_sqft_max' => round($maxCost / $floorArea),
                'per_sqft_avg' => round($avgCost / $floorArea)
            ],
            'cost_breakdown' => $breakdown,
            'multipliers_applied' => [
                'location' => $locationMultiplier,
                'floors' => $floorMultiplier,
                'accessibility' => $accessibilityMultiplier
            ],
            'disclaimer' => 'This is an approximate estimate based on current market rates. Actual costs may vary based on material prices, labor rates, site conditions, and specific requirements.',
            'generated_at' => date('Y-m-d H:i:s')
        ];
        
        // Save estimate to database for analytics
        $this->saveEstimate($data, $result);
        
        return $result;
    }
    
    /**
     * Get floor multiplier
     */
    private function getFloorMultiplier($floors) {
        switch ($floors) {
            case 1:
                return 1.0;
            case 2:
                return 1.15;
            case 3:
                return 1.25;
            default:
                return 1.35;
        }
    }
    
    /**
     * Get accessibility multiplier
     */
    private function getAccessibilityMultiplier($accessibility) {
        switch ($accessibility) {
            case 'easy':
                return 1.0;
            case 'moderate':
                return 1.1;
            case 'difficult':
                return 1.25;
            default:
                return 1.0;
        }
    }
    
    /**
     * Calculate additional features cost
     */
    private function calculateAdditionalFeatures($features) {
        if (!is_array($features)) {
            return 0;
        }
        
        $totalCost = 0;
        
        foreach ($features as $feature) {
            if (isset($this->rates['additional_features'][$feature])) {
                $totalCost += $this->rates['additional_features'][$feature];
            }
        }
        
        return $totalCost;
    }
    
    /**
     * Calculate cost breakdown
     */
    private function calculateBreakdown($totalCost, $additionalCost) {
        $baseCost = $totalCost - $additionalCost;
        
        return [
            'foundation_structure' => [
                'amount' => round($baseCost * 0.35),
                'percentage' => 35,
                'description' => 'Foundation, columns, beams, and structural work'
            ],
            'walls_roofing' => [
                'amount' => round($baseCost * 0.25),
                'percentage' => 25,
                'description' => 'Walls, roofing, and external structure'
            ],
            'electrical_plumbing' => [
                'amount' => round($baseCost * 0.20),
                'percentage' => 20,
                'description' => 'Electrical wiring, plumbing, and utilities'
            ],
            'finishing_interior' => [
                'amount' => round($baseCost * 0.15),
                'percentage' => 15,
                'description' => 'Flooring, painting, doors, windows, and interior work'
            ],
            'miscellaneous' => [
                'amount' => round($baseCost * 0.05),
                'percentage' => 5,
                'description' => 'Permits, supervision, and miscellaneous costs'
            ],
            'additional_features' => [
                'amount' => $additionalCost,
                'percentage' => round(($additionalCost / $totalCost) * 100),
                'description' => 'Swimming pool, garage, solar panels, and other features'
            ]
        ];
    }
    
    /**
     * Save estimate to database for analytics
     */
    private function saveEstimate($inputData, $result) {
        try {
            // Get user ID if authenticated
            $userId = null;
            if (isset($_SESSION['user']['user_id'])) {
                $userId = $_SESSION['user']['user_id'];
            }
            
            $sql = "INSERT INTO cost_estimates (user_id, project_type, building_type, floor_area, floors, district, bedrooms, bathrooms, accessibility, additional_features, estimated_min_cost, estimated_max_cost, estimated_avg_cost, session_id, ip_address) 
                    VALUES (:user_id, :project_type, :building_type, :floor_area, :floors, :district, :bedrooms, :bathrooms, :accessibility, :additional_features, :estimated_min_cost, :estimated_max_cost, :estimated_avg_cost, :session_id, :ip_address)";
            
            $this->db->insert($sql, [
                ':user_id' => $userId,
                ':project_type' => $inputData['project_type'],
                ':building_type' => $inputData['building_type'],
                ':floor_area' => $inputData['floor_area'],
                ':floors' => $inputData['floors'],
                ':district' => $inputData['district'],
                ':bedrooms' => $inputData['bedrooms'] ?? null,
                ':bathrooms' => $inputData['bathrooms'] ?? null,
                ':accessibility' => $inputData['accessibility'] ?? 'easy',
                ':additional_features' => json_encode($inputData['additional_features'] ?? []),
                ':estimated_min_cost' => $result['cost_estimate']['min_cost'],
                ':estimated_max_cost' => $result['cost_estimate']['max_cost'],
                ':estimated_avg_cost' => $result['cost_estimate']['avg_cost'],
                ':session_id' => session_id(),
                ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? null
            ]);
        } catch (Exception $e) {
            // Don't throw error if saving fails, just log it
            error_log("Failed to save cost estimate: " . $e->getMessage(), 3, LOG_PATH . 'cost_estimator.log');
        }
    }
    
    /**
     * Get cost estimation statistics (for admin)
     */
    public function getStatistics($dateFrom = null, $dateTo = null) {
        $conditions = [];
        $params = [];
        
        if ($dateFrom) {
            $conditions[] = "created_at >= :date_from";
            $params[':date_from'] = $dateFrom;
        }
        
        if ($dateTo) {
            $conditions[] = "created_at <= :date_to";
            $params[':date_to'] = $dateTo;
        }
        
        $whereClause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";
        
        // Overall statistics
        $overallSql = "SELECT 
                          COUNT(*) as total_estimates,
                          AVG(estimated_avg_cost) as avg_estimated_cost,
                          MIN(estimated_avg_cost) as min_estimated_cost,
                          MAX(estimated_avg_cost) as max_estimated_cost,
                          AVG(floor_area) as avg_floor_area
                       FROM cost_estimates $whereClause";
        
        $overall = $this->db->fetchOne($overallSql, $params);
        
        // By project type
        $projectTypeSql = "SELECT project_type, COUNT(*) as count, AVG(estimated_avg_cost) as avg_cost
                          FROM cost_estimates $whereClause
                          GROUP BY project_type
                          ORDER BY count DESC";
        
        $byProjectType = $this->db->fetchAll($projectTypeSql, $params);
        
        // By district
        $districtSql = "SELECT district, COUNT(*) as count, AVG(estimated_avg_cost) as avg_cost
                       FROM cost_estimates $whereClause
                       GROUP BY district
                       ORDER BY count DESC
                       LIMIT 10";
        
        $byDistrict = $this->db->fetchAll($districtSql, $params);
        
        // By building type
        $buildingTypeSql = "SELECT building_type, COUNT(*) as count, AVG(estimated_avg_cost) as avg_cost
                           FROM cost_estimates $whereClause
                           GROUP BY building_type
                           ORDER BY count DESC";
        
        $byBuildingType = $this->db->fetchAll($buildingTypeSql, $params);
        
        return [
            'overall' => $overall,
            'by_project_type' => $byProjectType,
            'by_district' => $byDistrict,
            'by_building_type' => $byBuildingType,
            'date_range' => [
                'from' => $dateFrom,
                'to' => $dateTo
            ]
        ];
    }
    
    /**
     * Get popular features
     */
    public function getPopularFeatures() {
        $sql = "SELECT additional_features FROM cost_estimates WHERE additional_features IS NOT NULL AND additional_features != '[]'";
        $estimates = $this->db->fetchAll($sql);
        
        $featureCounts = [];
        
        foreach ($estimates as $estimate) {
            $features = json_decode($estimate['additional_features'], true);
            if (is_array($features)) {
                foreach ($features as $feature) {
                    $featureCounts[$feature] = ($featureCounts[$feature] ?? 0) + 1;
                }
            }
        }
        
        arsort($featureCounts);
        
        return $featureCounts;
    }
}
?>
