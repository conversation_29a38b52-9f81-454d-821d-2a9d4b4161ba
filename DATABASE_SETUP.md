# Brick & Click Database Setup Guide

## Overview
This document provides comprehensive instructions for setting up the database for the Brick & Click construction contractor platform.

## Database Schema Overview

The database consists of 20+ tables organized into the following modules:

### Core Tables
- **users** - Base user table for all user types
- **customer_profiles** - Customer-specific information
- **contractor_profiles** - Contractor business information
- **admin_profiles** - Admin user permissions

### Service & Location Tables
- **service_categories** - Construction service types
- **districts** - Sri Lankan districts
- **contractor_services** - Contractor service offerings
- **contractor_service_areas** - Service coverage areas

### Quote & Project Management
- **quote_requests** - Customer quote requests
- **quote_responses** - Contractor quote responses
- **projects** - Active construction projects
- **project_updates** - Project progress tracking

### Payment System
- **payment_schedules** - Project payment milestones
- **payment_transactions** - Payment records

### Review & Rating System
- **reviews** - Customer reviews and ratings
- **review_responses** - Contractor responses to reviews

### Portfolio & Media
- **contractor_portfolios** - Contractor project portfolios
- **portfolio_images** - Portfolio image gallery

### Additional Features
- **customer_favorites** - Saved contractors
- **notifications** - System notifications
- **cost_estimates** - Cost calculator data
- **reports** - User reports and disputes
- **activity_logs** - System activity tracking

## Prerequisites

### System Requirements
- MySQL 8.0+ or MariaDB 10.4+
- PHP 7.4+ (for PHP examples)
- Web server (Apache/Nginx)
- Minimum 2GB RAM
- 10GB+ storage space

### Required MySQL Features
- InnoDB storage engine
- UTF8MB4 character set support
- JSON data type support
- Trigger support
- Stored procedure support

## Installation Steps

### 1. Database Creation

```bash
# Connect to MySQL as root
mysql -u root -p

# Create database
CREATE DATABASE brick_click_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# Create database user
CREATE USER 'brick_click_user'@'localhost' IDENTIFIED BY 'your_secure_password_here';
GRANT ALL PRIVILEGES ON brick_click_db.* TO 'brick_click_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. Schema Installation

```bash
# Import the database schema
mysql -u brick_click_user -p brick_click_db < database_schema.sql
```

### 3. Verify Installation

```sql
-- Check if all tables are created
USE brick_click_db;
SHOW TABLES;

-- Verify sample data
SELECT COUNT(*) FROM service_categories;
SELECT COUNT(*) FROM districts;
SELECT * FROM users WHERE user_type = 'admin';
```

## Configuration

### 1. Environment Variables

Create a `.env` file in your project root:

```env
# Database Configuration
DB_HOST=localhost
DB_NAME=brick_click_db
DB_USER=brick_click_user
DB_PASS=your_secure_password_here
DB_CHARSET=utf8mb4

# Application Settings
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost/brick-click

# File Upload Settings
MAX_FILE_SIZE=5242880
UPLOAD_PATH=/uploads
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf

# Email Settings (for notifications)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
```

### 2. PHP Configuration

Update your `database_config.php` file with your actual database credentials:

```php
// Update these constants in DatabaseConfig class
private const DB_HOST = 'localhost';
private const DB_NAME = 'brick_click_db';
private const DB_USER = 'brick_click_user';
private const DB_PASS = 'your_secure_password_here';
```

## Sample Data

The schema includes sample data for testing:

### Default Admin User
- **Email**: <EMAIL>
- **Password**: admin123 (change immediately in production)
- **Role**: Super Admin

### Sample Customers
- <EMAIL>
- <EMAIL>

### Sample Contractors
- <EMAIL> (BuildPro Construction)
- <EMAIL> (Lanka Builders)

### Service Categories
- Residential Construction
- Commercial Construction
- Renovation & Remodeling
- Electrical Work
- Plumbing
- Interior Design

### All 25 Sri Lankan Districts
Complete list with Sinhala and Tamil translations.

## Database Features

### 1. Automatic Triggers
- **Rating Updates**: Automatically recalculates contractor ratings when reviews are added/updated
- **Notifications**: Creates notifications for quote responses and acceptances
- **Activity Logging**: Tracks important user actions

### 2. Views for Performance
- **contractor_search_view**: Optimized contractor search with all relevant data
- **project_dashboard_view**: Project overview with customer/contractor details
- **admin_stats_view**: Dashboard statistics for admin panel

### 3. Stored Procedures
- **SearchContractors**: Advanced contractor search with filters
- **GetContractorStats**: Contractor performance statistics
- **CalculateCostEstimate**: Cost estimation calculations

### 4. Indexes for Performance
- Composite indexes for common query patterns
- Full-text search capabilities
- Optimized for contractor search and filtering

## Security Considerations

### 1. Password Security
- All passwords are hashed using PHP's `password_hash()`
- Minimum password requirements should be enforced in application
- Consider implementing 2FA for admin accounts

### 2. Data Validation
- All user inputs should be sanitized and validated
- Use prepared statements for all database queries
- Implement CSRF protection for forms

### 3. File Upload Security
- Validate file types and sizes
- Store uploaded files outside web root
- Scan files for malware
- Use secure file naming conventions

### 4. Access Control
- Implement role-based access control
- Use session management for authentication
- Log all administrative actions

## Maintenance Tasks

### Daily
```sql
-- Backup database
mysqldump --single-transaction --routines --triggers brick_click_db > backup_$(date +%Y%m%d).sql
```

### Weekly
```sql
-- Clean old activity logs (older than 6 months)
DELETE FROM activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 6 MONTH);

-- Clean old notifications (older than 3 months and read)
DELETE FROM notifications WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 MONTH) AND is_read = TRUE;
```

### Monthly
```sql
-- Optimize tables
OPTIMIZE TABLE users, contractor_profiles, quote_requests, reviews;

-- Analyze tables for query optimization
ANALYZE TABLE contractor_profiles, quote_requests, reviews;

-- Check for orphaned records
SELECT COUNT(*) FROM contractor_services cs 
LEFT JOIN contractor_profiles cp ON cs.contractor_id = cp.id 
WHERE cp.id IS NULL;
```

## Performance Optimization

### 1. Index Monitoring
```sql
-- Check index usage
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY,
    NULLABLE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'brick_click_db'
ORDER BY TABLE_NAME, INDEX_NAME;
```

### 2. Query Performance
```sql
-- Enable slow query log
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- Monitor slow queries
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;
```

### 3. Table Sizes
```sql
-- Monitor table growth
SELECT 
    TABLE_NAME,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size (MB)',
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'brick_click_db'
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;
```

## Troubleshooting

### Common Issues

1. **Character Set Problems**
   ```sql
   -- Check character sets
   SHOW VARIABLES LIKE 'character_set%';
   
   -- Fix if needed
   ALTER DATABASE brick_click_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **Permission Issues**
   ```sql
   -- Check user permissions
   SHOW GRANTS FOR 'brick_click_user'@'localhost';
   
   -- Grant missing permissions
   GRANT SELECT, INSERT, UPDATE, DELETE ON brick_click_db.* TO 'brick_click_user'@'localhost';
   ```

3. **Connection Issues**
   ```bash
   # Test connection
   mysql -u brick_click_user -p -h localhost brick_click_db
   
   # Check MySQL status
   systemctl status mysql
   ```

## Backup and Recovery

### Backup Strategy
```bash
#!/bin/bash
# Daily backup script
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/brick_click"
DB_NAME="brick_click_db"
DB_USER="brick_click_user"

# Create backup directory
mkdir -p $BACKUP_DIR

# Full backup
mysqldump -u $DB_USER -p --single-transaction --routines --triggers $DB_NAME > $BACKUP_DIR/full_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/full_backup_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
```

### Recovery
```bash
# Restore from backup
gunzip backup_20250119_120000.sql.gz
mysql -u brick_click_user -p brick_click_db < backup_20250119_120000.sql
```

## Support and Documentation

For additional support:
1. Check the application logs in `/logs/` directory
2. Review MySQL error logs
3. Monitor system resources (CPU, memory, disk space)
4. Consult MySQL documentation for advanced configuration

## Version History

- **v1.0** - Initial database schema with core functionality
- **v1.1** - Added cost estimation and portfolio features
- **v1.2** - Enhanced search capabilities and performance optimization

---

**Note**: Always test database changes in a development environment before applying to production.
