<?php
/**
 * Brick & Click API Configuration
 * Main configuration file for the API
 */

// Prevent direct access
if (!defined('API_ACCESS')) {
    http_response_code(403);
    exit('Direct access forbidden');
}

// Define API access constant
define('API_ACCESS', true);

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Asia/Colombo');

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'brick_click_db');
define('DB_USER', 'brick_click_user');
define('DB_PASS', 'your_secure_password_here');
define('DB_CHARSET', 'utf8mb4');

// Application Configuration
define('APP_NAME', 'Brick & Click');
define('APP_VERSION', '1.0.0');
define('APP_ENV', 'development'); // development, staging, production
define('APP_DEBUG', true);
define('APP_URL', 'http://localhost/arkitektur-1.0.0');
define('API_URL', APP_URL . '/api');

// Security Configuration
define('JWT_SECRET', 'your-super-secret-jwt-key-change-in-production');
define('JWT_EXPIRY', 86400); // 24 hours
define('PASSWORD_MIN_LENGTH', 8);
define('SESSION_TIMEOUT', 3600); // 1 hour

// File Upload Configuration
define('UPLOAD_MAX_SIZE', 5242880); // 5MB
define('UPLOAD_PATH', __DIR__ . '/../../uploads/');
define('UPLOAD_URL', APP_URL . '/uploads/');
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx']);
define('ALLOWED_FILE_TYPES', array_merge(ALLOWED_IMAGE_TYPES, ALLOWED_DOCUMENT_TYPES));

// Email Configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'Brick & Click');

// Pagination Configuration
define('DEFAULT_PAGE_SIZE', 12);
define('MAX_PAGE_SIZE', 100);

// Cache Configuration
define('CACHE_ENABLED', true);
define('CACHE_TTL', 3600); // 1 hour

// Rate Limiting
define('RATE_LIMIT_ENABLED', true);
define('RATE_LIMIT_REQUESTS', 100);
define('RATE_LIMIT_WINDOW', 3600); // 1 hour

// Logging Configuration
define('LOG_ENABLED', true);
define('LOG_PATH', __DIR__ . '/../../logs/');
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR

// Language Configuration
define('DEFAULT_LANGUAGE', 'en');
define('SUPPORTED_LANGUAGES', ['en', 'si', 'ta']);

// Cost Estimation Configuration
define('COST_RATES', [
    'project_types' => [
        'new-house' => [
            'basic' => ['min' => 8000, 'max' => 12000],
            'medium' => ['min' => 12000, 'max' => 18000],
            'luxury' => ['min' => 18000, 'max' => 30000]
        ],
        'renovation' => [
            'basic' => ['min' => 4000, 'max' => 7000],
            'medium' => ['min' => 7000, 'max' => 12000],
            'luxury' => ['min' => 12000, 'max' => 20000]
        ],
        'extension' => [
            'basic' => ['min' => 6000, 'max' => 10000],
            'medium' => ['min' => 10000, 'max' => 15000],
            'luxury' => ['min' => 15000, 'max' => 25000]
        ],
        'commercial' => [
            'basic' => ['min' => 10000, 'max' => 15000],
            'medium' => ['min' => 15000, 'max' => 22000],
            'luxury' => ['min' => 22000, 'max' => 35000]
        ]
    ],
    'location_multipliers' => [
        'colombo' => 1.2,
        'gampaha' => 1.1,
        'kalutara' => 1.0,
        'kandy' => 0.95,
        'matale' => 0.85,
        'galle' => 0.95,
        'default' => 0.9
    ],
    'additional_features' => [
        'pool' => 1500000,
        'garage' => 300000,
        'garden' => 200000,
        'solar' => 800000,
        'security' => 150000,
        'ac' => 500000
    ]
]);

// API Response Codes
define('HTTP_OK', 200);
define('HTTP_CREATED', 201);
define('HTTP_BAD_REQUEST', 400);
define('HTTP_UNAUTHORIZED', 401);
define('HTTP_FORBIDDEN', 403);
define('HTTP_NOT_FOUND', 404);
define('HTTP_METHOD_NOT_ALLOWED', 405);
define('HTTP_CONFLICT', 409);
define('HTTP_UNPROCESSABLE_ENTITY', 422);
define('HTTP_INTERNAL_SERVER_ERROR', 500);

// Create necessary directories
$directories = [
    UPLOAD_PATH,
    UPLOAD_PATH . 'profiles/',
    UPLOAD_PATH . 'documents/',
    UPLOAD_PATH . 'portfolios/',
    LOG_PATH
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Autoloader for classes
spl_autoload_register(function ($className) {
    $paths = [
        __DIR__ . '/../../api/classes/',
        __DIR__ . '/../../api/controllers/',
        __DIR__ . '/../../api/models/',
        __DIR__ . '/../../api/middleware/',
        __DIR__ . '/../../api/utils/'
    ];
    
    foreach ($paths as $path) {
        $file = $path . $className . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Global error handler
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    $error = [
        'severity' => $severity,
        'message' => $message,
        'file' => $file,
        'line' => $line,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    if (LOG_ENABLED) {
        error_log(json_encode($error), 3, LOG_PATH . 'errors.log');
    }
    
    if (APP_DEBUG) {
        throw new ErrorException($message, 0, $severity, $file, $line);
    }
    
    return true;
});

// Global exception handler
set_exception_handler(function($exception) {
    $error = [
        'message' => $exception->getMessage(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString(),
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    if (LOG_ENABLED) {
        error_log(json_encode($error), 3, LOG_PATH . 'exceptions.log');
    }
    
    if (APP_DEBUG) {
        http_response_code(HTTP_INTERNAL_SERVER_ERROR);
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => 'Internal server error',
            'debug' => $error
        ]);
    } else {
        http_response_code(HTTP_INTERNAL_SERVER_ERROR);
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => 'Internal server error'
        ]);
    }
    exit;
});

// CORS headers for API
if (isset($_SERVER['HTTP_ORIGIN'])) {
    header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
    header('Access-Control-Allow-Credentials: true');
    header('Access-Control-Max-Age: 86400');
}

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
        header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    }
    
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
        header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
    }
    
    exit(0);
}

// Set JSON content type for API responses
header('Content-Type: application/json; charset=utf-8');

// Start session for authentication
session_start();

// Initialize database connection
try {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
} catch (PDOException $e) {
    if (LOG_ENABLED) {
        error_log("Database connection failed: " . $e->getMessage(), 3, LOG_PATH . 'database.log');
    }
    
    http_response_code(HTTP_INTERNAL_SERVER_ERROR);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed'
    ]);
    exit;
}

// Helper functions
function response($data, $code = HTTP_OK) {
    http_response_code($code);
    echo json_encode($data);
    exit;
}

function success($data = null, $message = 'Success') {
    response([
        'success' => true,
        'message' => $message,
        'data' => $data
    ]);
}

function error($message, $code = HTTP_BAD_REQUEST, $details = null) {
    $response = [
        'success' => false,
        'error' => $message
    ];
    
    if ($details && APP_DEBUG) {
        $response['details'] = $details;
    }
    
    response($response, $code);
}

function validate_required_fields($data, $required_fields) {
    $missing = [];
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            $missing[] = $field;
        }
    }
    
    if (!empty($missing)) {
        error('Missing required fields: ' . implode(', ', $missing), HTTP_BAD_REQUEST);
    }
}

function sanitize_input($input) {
    if (is_array($input)) {
        return array_map('sanitize_input', $input);
    }
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

function log_activity($user_id, $action, $entity_type = null, $entity_id = null, $description = null) {
    global $pdo;
    
    try {
        $sql = "INSERT INTO activity_logs (user_id, action, entity_type, entity_id, description, ip_address, user_agent) 
                VALUES (:user_id, :action, :entity_type, :entity_id, :description, :ip_address, :user_agent)";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':user_id' => $user_id,
            ':action' => $action,
            ':entity_type' => $entity_type,
            ':entity_id' => $entity_id,
            ':description' => $description,
            ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    } catch (Exception $e) {
        if (LOG_ENABLED) {
            error_log("Failed to log activity: " . $e->getMessage(), 3, LOG_PATH . 'activity.log');
        }
    }
}

?>
